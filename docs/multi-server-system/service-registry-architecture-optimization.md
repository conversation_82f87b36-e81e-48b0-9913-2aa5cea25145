# 服务注册架构优化方案

## 📋 概述

本文档提出了对当前服务注册架构的全面优化方案，解决Auth服务接入混乱、命名不清晰、模块冗余等问题，建立清晰的分层服务注册架构。

## 🎯 核心问题分析

### **1. 当前架构问题**

#### **概念混乱**
```typescript
// ❌ 问题：Auth服务被强制分配区服ID
ServiceRegistryModule.forService('auth', {
  // Auth服务不应该有区服概念，但被强制分配到server_001
})
```

#### **架构层级不清**
- **Gateway**：网关层，需要发现所有服务
- **Auth**：全局服务层，跨区服共享
- **Character/Hero**：业务服务层，区服隔离

#### **模块冗余**
- `apps/gateway/src/modules/load-balancing`：旧的负载均衡模块
- `libs/service-registry`：新的区服感知服务注册中心
- 两套负载均衡逻辑并存，维护复杂

### **2. 服务特性对比**

| 服务类型 | 数据隔离 | 负载均衡范围 | 服务发现方式 | 典型服务 |
|---------|---------|-------------|-------------|---------|
| 网关服务 | 无状态 | 不适用 | 发现所有服务 | Gateway |
| 全局服务 | 全局共享 | 全局实例间 | 全局服务发现 | Auth, Payment |
| 区服服务 | 区服隔离 | 区服内实例间 | 区服感知发现 | Character, Hero |

## 🏗️ 优化架构设计

### **架构图**

```mermaid
graph TB
    subgraph "网关层"
        GW[Gateway]
        GW --> USD[UnifiedServiceDiscovery]
    end
    
    subgraph "全局服务层"
        AUTH[Auth Service]
        PAY[Payment Service]
        AUTH --> GSR[GlobalServiceRegistry]
        PAY --> GSR
    end
    
    subgraph "区服业务层"
        CHAR1[Character@server_001]
        CHAR2[Character@server_002]
        HERO1[Hero@server_001]
        HERO2[Hero@server_002]
        
        CHAR1 --> SSR[ServerAwareRegistry]
        CHAR2 --> SSR
        HERO1 --> SSR
        HERO2 --> SSR
    end
    
    subgraph "Redis存储层"
        REDIS[(Redis)]
        GSR --> REDIS
        SSR --> REDIS
        USD --> REDIS
    end
```

### **核心设计原则**

1. **分层清晰**：网关层、全局服务层、区服服务层职责明确
2. **命名简洁**：`forRoot()`, `forGlobal()`, `forServer()`
3. **统一发现**：网关使用统一服务发现，自动路由到合适的服务
4. **负载均衡**：全局服务和区服服务都支持负载均衡，但范围不同

## 🔧 实现方案

### **1. ServiceRegistryModule重构**

```typescript
/**
 * 统一服务注册模块
 * 
 * 使用模式：
 * - forRoot(): 网关使用，提供统一服务发现能力
 * - forGlobal(): 全局服务使用，如Auth、Payment
 * - forServer(): 区服服务使用，如Character、Hero
 */
@Module({})
export class ServiceRegistryModule {

  /**
   * 网关模式：提供统一的服务发现能力
   * 自动识别全局服务和区服服务，智能路由
   */
  static forRoot(): DynamicModule {
    return {
      module: ServiceRegistryModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(),
        RedisModule.forRootAsync({
          service: 'service-registry',
          useFactory: (configService: ConfigService) => ({
            host: configService.get('REDIS_HOST'),
            port: configService.get('REDIS_PORT'),
            password: configService.get('REDIS_PASSWORD'),
            keyPrefix: '',
          }),
          inject: [ConfigService],
        }),
      ],
      providers: [
        // 统一服务发现（网关专用）
        UnifiedServiceDiscoveryService,
        
        // 全局服务注册中心
        GlobalServiceRegistryService,
        GlobalLoadBalancerService,
        
        // 区服感知服务注册中心
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        
        // 实例生命周期管理
        InstanceLifecycleService,
      ],
      exports: [
        UnifiedServiceDiscoveryService,  // 网关主要使用
        GlobalServiceRegistryService,
        ServerAwareRegistryService,
      ],
    };
  }

  /**
   * 全局服务模式：用于Auth等跨区服服务
   * 特点：数据全局共享，支持全局负载均衡
   */
  static forGlobal(
    serviceName: string,
    options?: {
      weight?: number;
      metadata?: Record<string, any>;
      healthCheckPath?: string;
    }
  ): DynamicModule {
    return {
      module: ServiceRegistryModule,
      global: true,
      imports: [
        ConfigModule,
        RedisModule.forRootAsync({
          service: 'global-service-registry',
          useFactory: (configService: ConfigService) => ({
            host: configService.get('REDIS_HOST'),
            port: configService.get('REDIS_PORT'),
            password: configService.get('REDIS_PASSWORD'),
            keyPrefix: '',
          }),
          inject: [ConfigService],
        }),
      ],
      providers: [
        GlobalServiceRegistryService,
        {
          provide: 'GLOBAL_SERVICE_CONFIG',
          useValue: {
            serviceName,
            weight: options?.weight ?? 1,
            metadata: options?.metadata ?? {},
            healthCheckPath: options?.healthCheckPath ?? '/health',
          },
        },
        GlobalServiceAutoRegistrationService,
      ],
      exports: [
        GlobalServiceRegistryService,
        GlobalServiceAutoRegistrationService,
      ],
    };
  }

  /**
   * 区服服务模式：用于Character等业务服务
   * 特点：数据区服隔离，支持区服内负载均衡
   */
  static forServer(
    serviceName: string,
    options?: {
      weight?: number;
      metadata?: Record<string, any>;
      healthCheckPath?: string;
    }
  ): DynamicModule {
    return {
      module: ServiceRegistryModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(),
        RedisModule.forRootAsync({
          service: 'server-aware-registry',
          useFactory: (configService: ConfigService) => ({
            host: configService.get('REDIS_HOST'),
            port: configService.get('REDIS_PORT'),
            password: configService.get('REDIS_PASSWORD'),
            keyPrefix: '',
          }),
          inject: [ConfigService],
        }),
      ],
      providers: [
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        {
          provide: 'SERVICE_REGISTRATION_CONFIG',
          useValue: {
            serviceName,
            autoRegister: true,
            weight: options?.weight ?? 1,
            metadata: options?.metadata ?? {},
            healthCheckPath: options?.healthCheckPath ?? '/health',
          },
        },
        ServiceAutoRegistrationService,
      ],
      exports: [
        ServerAwareRegistryService,
        ServiceAutoRegistrationService,
      ],
    };
  }
}
```

### **2. UnifiedServiceDiscoveryService**

```typescript
/**
 * 统一服务发现服务
 * 
 * 网关专用，自动识别服务类型并选择合适的发现方式：
 * - 全局服务：使用GlobalServiceRegistry
 * - 区服服务：使用ServerAwareRegistry
 */
@Injectable()
export class UnifiedServiceDiscoveryService {
  private readonly logger = new Logger(UnifiedServiceDiscoveryService.name);
  
  // 全局服务列表（可配置化）
  private readonly GLOBAL_SERVICES = new Set([
    'auth', 'payment', 'notification', 'analytics'
  ]);

  constructor(
    private readonly globalRegistry: GlobalServiceRegistryService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 统一服务发现入口
   * @param serviceName 服务名称
   * @param context 请求上下文（包含区服信息等）
   */
  async discoverService(
    serviceName: string, 
    context?: ServiceDiscoveryContext
  ): Promise<ServiceInstance> {
    
    if (this.isGlobalService(serviceName)) {
      return this.discoverGlobalService(serviceName);
    } else {
      return this.discoverServerService(serviceName, context?.serverId);
    }
  }

  /**
   * 发现全局服务
   */
  private async discoverGlobalService(serviceName: string): Promise<ServiceInstance> {
    this.logger.debug(`🌍 发现全局服务: ${serviceName}`);
    
    const instances = await this.globalRegistry.getHealthyInstances(serviceName);
    if (instances.length === 0) {
      throw new ServiceUnavailableException(`全局服务 ${serviceName} 不可用`);
    }

    // 使用全局负载均衡器选择实例
    return this.globalRegistry.selectInstance(serviceName, 'round-robin');
  }

  /**
   * 发现区服服务
   */
  private async discoverServerService(
    serviceName: string, 
    serverId?: string
  ): Promise<ServiceInstance> {
    const targetServerId = serverId || this.getDefaultServerId();
    this.logger.debug(`🏰 发现区服服务: ${serviceName}@${targetServerId}`);
    
    return this.serverAwareRegistry.selectInstance(serviceName, targetServerId, 'round-robin');
  }

  /**
   * 判断是否为全局服务
   */
  private isGlobalService(serviceName: string): boolean {
    return this.GLOBAL_SERVICES.has(serviceName);
  }

  private getDefaultServerId(): string {
    return this.configService.get('DEFAULT_SERVER_ID', 'server_001');
  }
}

interface ServiceDiscoveryContext {
  serverId?: string;
  userId?: string;
  requestId?: string;
}
```

### **3. GlobalServiceRegistryService**

```typescript
/**
 * 全局服务注册中心
 * 
 * 管理跨区服的全局服务，如Auth、Payment等
 * 特点：
 * - 数据全局共享
 * - 支持全局负载均衡
 * - 健康检查和故障转移
 */
@Injectable()
export class GlobalServiceRegistryService implements OnModuleInit {
  private readonly logger = new Logger(GlobalServiceRegistryService.name);
  private healthCheckInterval: NodeJS.Timeout;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    this.logger.log('🌍 全局服务注册中心启动');
    this.startHealthCheck();
  }

  /**
   * 注册全局服务实例
   */
  async registerInstance(instance: GlobalServiceInstance): Promise<string> {
    const instanceId = `${instance.serviceName}-global-${Date.now()}`;
    const key = this.getServiceKey(instance.serviceName);
    
    const instanceData: StoredServiceInstance = {
      ...instance,
      instanceId,
      serviceType: 'global',
      registeredAt: new Date().toISOString(),
      lastHeartbeat: new Date().toISOString(),
      healthy: true,
    };

    await this.redisService.hset(key, instanceId, JSON.stringify(instanceData));
    
    this.logger.log(`✅ 全局服务注册: ${instance.serviceName} (${instanceId})`);
    return instanceId;
  }

  /**
   * 获取健康的服务实例
   */
  async getHealthyInstances(serviceName: string): Promise<GlobalServiceInstance[]> {
    const key = this.getServiceKey(serviceName);
    const instances = await this.redisService.hgetall(key);
    
    return Object.values(instances)
      .map(data => JSON.parse(data))
      .filter(instance => instance.healthy);
  }

  /**
   * 选择服务实例（负载均衡）
   */
  async selectInstance(serviceName: string, strategy: string = 'round-robin'): Promise<ServiceInstance> {
    const instances = await this.getHealthyInstances(serviceName);
    
    if (instances.length === 0) {
      throw new ServiceUnavailableException(`全局服务 ${serviceName} 无可用实例`);
    }

    // 简化的轮询负载均衡
    const selectedIndex = Math.floor(Math.random() * instances.length);
    const selected = instances[selectedIndex];
    
    return {
      id: selected.instanceId,
      serviceName: selected.serviceName,
      host: selected.host,
      port: selected.port,
      healthy: selected.healthy,
      weight: selected.weight,
      metadata: selected.metadata,
    };
  }

  /**
   * 注销服务实例
   */
  async unregisterInstance(serviceName: string, instanceId: string): Promise<void> {
    const key = this.getServiceKey(serviceName);
    await this.redisService.hdel(key, instanceId);
    
    this.logger.log(`✅ 全局服务注销: ${serviceName} (${instanceId})`);
  }

  private getServiceKey(serviceName: string): string {
    return `global_services:${serviceName}`;
  }

  private startHealthCheck(): void {
    const interval = this.configService.get('GLOBAL_SERVICE_HEALTH_CHECK_INTERVAL', 30000);
    
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, interval);
  }

  private async performHealthCheck(): Promise<void> {
    // 健康检查逻辑
    this.logger.debug('🏥 执行全局服务健康检查');
  }
}
```

## 📊 Redis键名设计

### **全局服务**
```
global_services:auth -> Hash {
  "auth-global-**********": {
    "instanceId": "auth-global-**********",
    "serviceName": "auth",
    "serviceType": "global",
    "host": "127.0.0.1",
    "port": 3001,
    "healthy": true,
    "weight": 1,
    "registeredAt": "2025-08-02T10:00:00.000Z",
    "lastHeartbeat": "2025-08-02T10:05:00.000Z",
    "metadata": {
      "version": "1.0.0",
      "features": ["authentication", "authorization"]
    }
  }
}
```

### **区服服务**
```
service_registry:instances:character:server_001 -> Hash {
  "character-server_001-1-**********": {
    "instanceId": "character-server_001-1-**********",
    "serviceName": "character",
    "serviceType": "server-aware",
    "serverId": "server_001",
    "host": "127.0.0.1",
    "port": 3002,
    "healthy": true,
    "weight": 1,
    "registeredAt": "2025-08-02T10:00:00.000Z",
    "lastHeartbeat": "2025-08-02T10:05:00.000Z"
  }
}
```

## 🔄 迁移计划

### **阶段1：基础架构搭建**
1. 创建`UnifiedServiceDiscoveryService`
2. 创建`GlobalServiceRegistryService`
3. 重构`ServiceRegistryModule`的三种模式

### **阶段2：服务迁移**
1. 迁移Auth服务到`forGlobal()`模式
2. 更新Gateway使用`forRoot()`模式
3. 验证Character等服务的`forServer()`模式

### **阶段3：清理优化**
1. 移除`apps/gateway/src/modules/load-balancing`模块
2. 清理冗余代码和配置
3. 完善文档和测试

## 📋 配置示例

### **Gateway配置**
```typescript
@Module({
  imports: [
    ServiceRegistryModule.forRoot(), // 统一服务发现
  ],
})
export class GatewayAppModule {}
```

### **Auth服务配置**
```typescript
@Module({
  imports: [
    ServiceRegistryModule.forGlobal('auth', {
      weight: 1,
      metadata: {
        version: '1.0.0',
        features: ['authentication', 'authorization'],
        description: '全局认证服务',
      },
    }),
  ],
})
export class AuthAppModule {}
```

### **Character服务配置**
```typescript
@Module({
  imports: [
    ServiceRegistryModule.forServer('character', {
      weight: 1,
      metadata: {
        version: '1.0.0',
        features: ['character-management'],
        description: '角色管理服务',
      },
    }),
  ],
})
export class CharacterAppModule {}
```

## ✅ 预期收益

1. **架构清晰**：三层架构职责明确，概念不再混乱
2. **命名简洁**：`forRoot/forGlobal/forServer`简洁易懂
3. **功能完整**：全局服务和区服服务都支持负载均衡
4. **维护简单**：统一的服务注册架构，减少维护成本
5. **扩展性强**：可以轻松添加新的全局服务或区服服务

## 🚨 风险评估与缓解

### **潜在风险**

1. **服务中断风险**
   - **风险**：迁移过程中可能导致服务暂时不可用
   - **缓解**：采用渐进式迁移，保持向后兼容

2. **配置复杂性**
   - **风险**：新架构增加配置复杂度
   - **缓解**：提供详细的配置模板和迁移指南

3. **性能影响**
   - **风险**：统一服务发现可能增加延迟
   - **缓解**：实现缓存机制，优化查询性能

### **缓解措施**

1. **渐进式迁移**：先迁移Auth服务，验证成功后再迁移其他服务
2. **向后兼容**：保留旧接口一段时间，确保平滑过渡
3. **充分测试**：每个阶段都进行完整的功能测试
4. **监控告警**：实时监控服务注册状态和性能指标

## 🧪 测试策略

### **单元测试**
- `UnifiedServiceDiscoveryService`的服务类型识别
- `GlobalServiceRegistryService`的注册和发现逻辑
- 负载均衡算法的正确性

### **集成测试**
- Gateway到Auth服务的完整调用链路
- Gateway到Character服务的完整调用链路
- 服务注册和注销的完整流程

### **性能测试**
- 服务发现的响应时间
- 负载均衡的分布均匀性
- 高并发下的系统稳定性

### **故障测试**
- 服务实例下线时的故障转移
- Redis连接异常时的降级处理
- 网络分区时的服务发现行为

## 📈 监控指标

### **服务注册指标**
- 全局服务注册成功率
- 区服服务注册成功率
- 服务实例健康检查通过率

### **服务发现指标**
- 服务发现响应时间
- 服务发现成功率
- 负载均衡分布情况

### **业务指标**
- Gateway代理请求成功率
- Auth服务调用成功率
- 各业务服务调用成功率

## 🔧 实施检查清单

### **阶段1：基础架构搭建**
- [ ] 创建`UnifiedServiceDiscoveryService`
- [ ] 创建`GlobalServiceRegistryService`
- [ ] 创建`GlobalServiceAutoRegistrationService`
- [ ] 重构`ServiceRegistryModule`三种模式
- [ ] 编写单元测试
- [ ] 更新类型定义

### **阶段2：Auth服务迁移**
- [ ] 更新Auth服务配置为`forGlobal()`
- [ ] 测试Auth服务自动注册
- [ ] 验证全局服务发现功能
- [ ] 测试Auth服务负载均衡
- [ ] 运行完整的HTTP代理测试

### **阶段3：Gateway更新**
- [ ] 更新Gateway配置为`forRoot()`
- [ ] 更新ProxyController使用`UnifiedServiceDiscoveryService`
- [ ] 更新RouteResolverService
- [ ] 测试智能服务路由
- [ ] 验证全局和区服服务的混合调用

### **阶段4：清理优化**
- [ ] 移除`apps/gateway/src/modules/load-balancing`模块
- [ ] 清理冗余的导入和配置
- [ ] 更新相关文档
- [ ] 运行完整的回归测试
- [ ] 性能基准测试

## 📚 相关文档

- [业务服务接入分区分服架构开发指南](./business-service-integration-guide.md)
- [ServiceRegistryModule API 文档](./service-registry-api.md)
- [Character服务接入实践案例](./character-service-integration-case-study.md)
- [HTTP代理迁移分析](../gateway/docs/http-proxy-migration-analysis.md)

## 🤝 审核要点

请重点审核以下方面：

1. **架构设计**：三层架构是否合理？命名是否清晰？
2. **技术实现**：`UnifiedServiceDiscoveryService`的设计是否可行？
3. **迁移策略**：渐进式迁移计划是否安全？
4. **性能考虑**：统一服务发现是否会影响性能？
5. **扩展性**：架构是否支持未来的扩展需求？

---

**文档状态**: 📝 待审核
**预计实施时间**: 2-3天
**影响范围**: Gateway、Auth、所有业务服务
**优先级**: 🔥 高优先级（解决架构混乱问题）
