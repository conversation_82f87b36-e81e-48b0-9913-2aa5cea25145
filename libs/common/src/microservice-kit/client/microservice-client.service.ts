import { Injectable, Inject, Optional, Logger } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import { ModuleRef } from '@nestjs/core';
import { timeout } from 'rxjs/operators';
import { MicroserviceKitConfig } from '../config/microservice.config';
import { MicroserviceName } from '@shared/constants';

// 导入服务注册中心
import { ServerAwareRegistryService } from '@libs/service-registry';
// 导入连接池服务
import { ConnectionPoolService } from './connection-pool.service';
// 导入工具服务
import { ContextExtractorService } from '../utils/context-extractor.service';
import { LoadBalancerService } from '../utils/load-balancer.service';

// 服务实例类型（临时定义，避免循环依赖）
interface ServiceInstance {
  instanceName: string;
  host: string;
  port: number;
  serviceName: string;
  serverId: string;
}

/**
 * 微服务客户端服务
 * 提供统一的微服务调用接口
 */
@Injectable()
export class MicroserviceClientService {
  private readonly logger = new Logger(MicroserviceClientService.name);
  private clients = new Map<MicroserviceName, ClientProxy>();
  private connectedServices: MicroserviceName[];

  constructor(
      @Inject('MICROSERVICE_CONFIG') private config: MicroserviceKitConfig,
      @Inject('CONNECTED_SERVICES') connectedServices: MicroserviceName[],
      // 新增：可选注入服务注册中心（保持向后兼容）
      @Optional() private serviceRegistry?: ServerAwareRegistryService,
      // 新增：可选注入连接池服务
      @Optional() private connectionPool?: ConnectionPoolService,
      // 新增：可选注入工具服务
      @Optional() private contextExtractor?: ContextExtractorService,
      @Optional() private loadBalancer?: LoadBalancerService,
  ) {
    this.connectedServices = connectedServices;
    this.initializeClients();
  }

  /**
   * 初始化客户端连接
   */
  private initializeClients() {
    // 只初始化已连接的服务
    this.connectedServices.forEach(serviceName => {
      const service = this.config.services[serviceName];
      if (!service) {
        console.warn(`⚠️ 服务配置未找到: ${serviceName}`);
        return;
      }

      try {
        // 使用 ClientProxyFactory 直接创建客户端
        const client = ClientProxyFactory.create({
          transport: service.transport,
          options: service.options,
        });

        this.clients.set(serviceName, client);
        console.log(`✅ 微服务客户端已创建: ${serviceName} -> ${service.name}`);
        console.log(`🔗 连接配置: ${JSON.stringify(service.options)}`);
      } catch (error) {
        console.error(`❌ 微服务客户端创建失败: ${serviceName}`, error);
      }
    });
  }

  /**
   * 获取指定服务的客户端
   */
  getClient(serviceName: MicroserviceName): ClientProxy {
    const client = this.clients.get(serviceName);
    if (!client) {
      const availableServices = this.getAvailableServices().join(', ');
      throw new Error(
          `微服务客户端未找到: ${serviceName}。` +
          `可用服务: [${availableServices}]。` +
          `请检查服务是否已在模块中注册。`
      );
    }
    return client;
  }

  /**
   * 统一调用方法（保持现有API完全不变）
   * 内部智能判断使用传统调用还是区服感知调用
   */
  async call<T = any>(serviceName: MicroserviceName, pattern: string, data?: any): Promise<T> {
    // 1. Auth服务始终使用传统Redis调用
    if (serviceName === 'auth') {
      return this.callTraditional(serviceName, pattern, data);
    }

    // 2. 检查是否启用区服感知功能
    if (!this.isServerAwareEnabled()) {
      return this.callTraditional(serviceName, pattern, data);
    }

    // 3. 尝试提取区服ID
    const serverId = this.contextExtractor?.extractServerId(data) || this.extractServerId(data);
    if (!serverId) {
      this.logger.warn(`⚠️ 无法提取区服ID，使用传统调用: ${serviceName}.${pattern}`);
      return this.callTraditional(serviceName, pattern, data);
    }

    // 4. 使用区服感知调用
    return this.callServerAware(serviceName, serverId, pattern, data);
  }

  /**
   * 传统Redis调用（保持现有逻辑）
   */
  private async callTraditional<T>(serviceName: MicroserviceName, pattern: string, data?: any): Promise<T> {
    const client = this.getClient(serviceName);
    try {
      const timeoutMs = serviceName === 'auth' ? 3000 : 5000;
      const result = await client.send<T>(pattern, data)
        .pipe(timeout(timeoutMs))
        .toPromise();
      this.logger.log(`📡 传统调用成功: ${serviceName}.${pattern}`);
      return result;
    } catch (error) {
      this.logger.error(`❌ 传统调用失败: ${serviceName}.${pattern}`, error);
      throw error;
    }
  }

  /**
   * 区服感知调用（基于现有callInstance优化）
   */
  private async callServerAware<T>(
    serviceName: MicroserviceName,
    serverId: string,
    pattern: string,
    data?: any
  ): Promise<T> {
    this.logger.log(`🎯 区服感知调用: ${serviceName}.${pattern}@${serverId}`);

    // 1. 通过负载均衡器选择实例
    const instance = (this.loadBalancer ? await this.loadBalancer.selectInstance(serviceName, serverId) : null) ||
                     await this.fallbackSelectInstance(serviceName, serverId);

    if (!instance) {
      this.logger.warn(`未找到健康实例，降级到传统调用: ${serviceName}@${serverId}`);
      return this.callTraditional(serviceName, pattern, data);
    }

    // 2. 使用现有的callInstance方法
    return this.callInstance(instance, pattern, data);
  }

  /**
   * 发送事件到微服务
   */
  emit(serviceName: MicroserviceName, pattern: string, data?: any): void {
    const client = this.getClient(serviceName);
    client.emit(pattern, data);
    console.log(`📤 事件已发送: ${serviceName}.${pattern}`);
  }

  /**
   * 获取所有可用的服务列表
   */
  getAvailableServices(): MicroserviceName[] {
    return Array.from(this.clients.keys());
  }

  /**
   * 检查服务是否可用
   */
  isServiceAvailable(serviceName: MicroserviceName): boolean {
    return this.clients.has(serviceName);
  }

  /**
   * 调用特定的服务实例（优化版本，支持连接池）
   * 用于区服感知的路由
   */
  async callInstance<T = any>(instance: ServiceInstance, pattern: string, data?: any): Promise<T> {
    try {
      let client: ClientProxy;
      let shouldCloseClient = false;

      // 使用连接池（如果可用）
      if (this.connectionPool) {
        client = await this.connectionPool.getConnection(instance);
      } else {
        // 降级到原有逻辑：创建临时客户端
        client = ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: instance.host,
            port: instance.port,
          },
        });
        shouldCloseClient = true;
      }

      // 设置超时时间
      const timeoutMs = instance.serviceName === 'auth' ? 3000 : 5000;

      const result = await client.send<T>(pattern, data)
        .pipe(timeout(timeoutMs))
        .toPromise();

      this.logger.log(`📡 实例调用成功: ${instance.instanceName}.${pattern}`);

      // 如果是临时客户端，需要关闭
      if (shouldCloseClient) {
        await client.close();
      }

      return result;
    } catch (error) {
      this.logger.error(`❌ 实例调用失败: ${instance.instanceName}.${pattern}`, error);

      // 更新连接池错误统计
      if (this.connectionPool) {
        const instanceKey = `${instance.host}:${instance.port}`;
        this.connectionPool.updateErrorStats(instanceKey);
      }

      throw error;
    }
  }

  /**
   * 批量调用多个服务
   */
  async callMultiple<T = any>(
      calls: Array<{ serviceName: MicroserviceName; pattern: string; data?: any }>
  ): Promise<T[]> {
    const promises = calls.map(call =>
        this.call<T>(call.serviceName, call.pattern, call.data)
    );
    return Promise.all(promises);
  }

  /**
   * 从调用数据中提取区服ID
   */
  private extractServerId(data?: any): string | null {
    if (!data) return this.getDefaultServerId();

    // 1. 直接从数据中提取
    if (data.serverId) return data.serverId;

    // 2. 从服务器上下文中提取
    if (data.serverContext?.serverId) return data.serverContext.serverId;

    // 3. 从用户ID推断（可扩展）
    if (data.userId || data.characterId) {
      return this.inferServerIdFromUser(data.userId || data.characterId);
    }

    // 4. 返回默认区服ID
    return this.getDefaultServerId();
  }

  /**
   * 从用户ID推断区服ID（可根据业务逻辑实现）
   */
  private inferServerIdFromUser(userId: string): string | null {
    // 简单示例：可以查询缓存或数据库获取用户所在区服
    // 这里返回null，让业务代码显式传递serverId
    return null;
  }

  /**
   * 获取默认区服ID
   */
  private getDefaultServerId(): string | null {
    return process.env.SERVER_ID || process.env.DEFAULT_SERVER_ID || null;
  }

  /**
   * 降级选择实例（当负载均衡器不可用时）
   */
  private async fallbackSelectInstance(serviceName: MicroserviceName, serverId: string): Promise<any | null> {
    if (!this.serviceRegistry) {
      return null;
    }

    try {
      const instances = await this.serviceRegistry.getHealthyInstances(serviceName, serverId);
      if (!instances || instances.length === 0) {
        return null;
      }

      // 简单选择第一个实例
      return instances[0];
    } catch (error) {
      this.logger.error(`降级选择实例失败: ${serviceName}@${serverId}`, error);
      return null;
    }
  }

  /**
   * 检查是否启用区服感知功能
   */
  private isServerAwareEnabled(): boolean {
    return this.serviceRegistry !== undefined &&
           process.env.SERVER_AWARE_MICROSERVICE_ENABLED !== 'false';
  }
}
