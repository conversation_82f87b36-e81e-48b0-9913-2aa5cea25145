import { Injectable, Logger, Optional } from '@nestjs/common';
import { ServerAwareRegistryService, ServerAwareLoadBalancerService } from '@libs/service-registry';
import { MicroserviceName } from '@shared/constants';

/**
 * 负载均衡器服务
 * 负责在多个服务实例中选择最佳实例
 */
@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);
  
  // 轮询计数器
  private readonly roundRobinCounters = new Map<string, number>();

  constructor(
    @Optional() private serviceRegistry?: ServerAwareRegistryService,
    @Optional() private serverAwareLoadBalancer?: ServerAwareLoadBalancerService,
  ) {}

  /**
   * 选择服务实例
   * 基于现有的 ServerAwareMicroserviceClientService.loadBalancer.selectInstance 优化
   */
  async selectInstance(
    serviceName: MicroserviceName,
    serverId: string,
    strategy: 'round-robin' | 'random' | 'least-connections' = 'round-robin'
  ): Promise<any | null> {
    // 优先使用专业的负载均衡器
    if (this.serverAwareLoadBalancer) {
      try {
        const instance = await this.serverAwareLoadBalancer.selectInstance(serviceName, serverId, strategy);
        if (instance) {
          this.logger.debug(`专业负载均衡器选择实例: ${instance.instanceName} (策略: ${strategy})`);
          return instance;
        }
      } catch (error) {
        this.logger.warn(`专业负载均衡器失败，降级到简单负载均衡: ${error.message}`);
      }
    }

    // 降级到简单负载均衡
    return this.simpleLoadBalance(serviceName, serverId, strategy);
  }

  /**
   * 简单负载均衡（降级方案）
   */
  private async simpleLoadBalance(
    serviceName: MicroserviceName,
    serverId: string,
    strategy: string
  ): Promise<any | null> {
    if (!this.serviceRegistry) {
      this.logger.warn('服务注册中心未可用，无法进行负载均衡');
      return null;
    }

    // 获取健康的服务实例
    let instances: any[];
    try {
      instances = await this.serviceRegistry.getHealthyInstances(serviceName, serverId);
    } catch (error) {
      this.logger.error(`获取健康实例失败: ${serviceName}@${serverId}`, error);
      return null;
    }

    if (!instances || instances.length === 0) {
      this.logger.warn(`没有可用的健康实例: ${serviceName}@${serverId}`);
      return null;
    }

    // 根据策略选择实例
    let selectedInstance: any;
    switch (strategy) {
      case 'round-robin':
        selectedInstance = this.selectRoundRobin(serviceName, serverId, instances);
        break;
      case 'random':
        selectedInstance = this.selectRandom(instances);
        break;
      case 'least-connections':
        selectedInstance = this.selectLeastConnections(instances);
        break;
      default:
        selectedInstance = this.selectRoundRobin(serviceName, serverId, instances);
    }

    this.logger.debug(`简单负载均衡选择实例: ${selectedInstance?.instanceName} (策略: ${strategy})`);
    return selectedInstance;
  }

  /**
   * 轮询选择
   */
  private selectRoundRobin(
    serviceName: MicroserviceName, 
    serverId: string, 
    instances: any[]
  ): any {
    const key = `${serviceName}-${serverId}`;
    const currentCount = this.roundRobinCounters.get(key) || 0;
    const selectedIndex = currentCount % instances.length;
    
    this.roundRobinCounters.set(key, currentCount + 1);
    
    const selected = instances[selectedIndex];
    this.logger.debug(`轮询选择实例: ${selected.instanceName} (${selectedIndex + 1}/${instances.length})`);
    
    return selected;
  }

  /**
   * 随机选择
   */
  private selectRandom(instances: any[]): any {
    const randomIndex = Math.floor(Math.random() * instances.length);
    const selected = instances[randomIndex];
    
    this.logger.debug(`随机选择实例: ${selected.instanceName}`);
    return selected;
  }

  /**
   * 最少连接选择
   */
  private selectLeastConnections(instances: any[]): any {
    // 简单实现：选择第一个实例
    // 实际实现需要跟踪每个实例的连接数
    const selected = instances.reduce((prev, current) => {
      const prevConnections = this.getInstanceConnections(prev.id || prev.instanceName);
      const currentConnections = this.getInstanceConnections(current.id || current.instanceName);
      return prevConnections <= currentConnections ? prev : current;
    });
    
    this.logger.debug(`最少连接选择实例: ${selected.instanceName}`);
    return selected;
  }

  /**
   * 获取实例连接数（需要与连接池集成）
   */
  private getInstanceConnections(instanceId: string): number {
    // 这里需要与ConnectionPoolService集成
    // 临时返回随机数模拟
    return Math.floor(Math.random() * 10);
  }

  /**
   * 重置轮询计数器
   */
  resetCounters(): void {
    this.roundRobinCounters.clear();
    this.logger.debug('负载均衡计数器已重置');
  }

  /**
   * 获取负载均衡统计信息
   */
  getStats(): Map<string, number> {
    return new Map(this.roundRobinCounters);
  }

  /**
   * 检查服务注册中心是否可用
   */
  isServiceRegistryAvailable(): boolean {
    return this.serviceRegistry !== undefined;
  }
}
