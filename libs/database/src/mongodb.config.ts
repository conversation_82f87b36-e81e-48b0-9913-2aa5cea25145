/**
 * MongoDB数据库配置
 */

import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions } from '@nestjs/mongoose';

export interface DatabaseConfig {
  uri: string;
  dbName: string;
  options: any; // 使用any类型避免类型冲突
}

/**
 * 创建MongoDB配置（支持分区分服）
 *
 * @param configService 配置服务
 * @param serviceName 服务名称
 * @returns Mongoose模块配置
 */
export const createMongoConfig = (configService: ConfigService, serviceName: string): MongooseModuleOptions => {
  // 获取区服ID（如果存在）
  const serverId = configService.get<string>('SERVER_ID');

  // 构建数据库URI的环境变量名
  const uriKey = serverId
    ? `${serviceName.toUpperCase()}_MONGODB_URI_${serverId.toUpperCase()}`
    : `${serviceName.toUpperCase()}_MONGODB_URI`;

  // 获取数据库URI，支持多级回退
  const uri = configService.get<string>(uriKey) ||
              configService.get<string>(`${serviceName.toUpperCase()}_MONGODB_URI`) ||
              configService.get<string>('MONGODB_URI');

  if (!uri) {
    throw new Error(`数据库URI未配置: ${uriKey} 或 ${serviceName.toUpperCase()}_MONGODB_URI 或 MONGODB_URI`);
  }

  const configs: Record<string, DatabaseConfig> = {
    // 认证服务数据库（全局唯一，不分区服）
    auth: {
      uri: configService.get<string>('AUTH_MONGODB_URI') || configService.get<string>('MONGODB_URI'),
      dbName: 'auth_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },

    // 角色服务数据库（支持分区分服）
    character: {
      uri,
      // TODO: 暂时使用统一数据库，待区服数据库创建后启用分区分服
      // 正确逻辑：dbName: serverId ? `character_db_${serverId}` : 'character_db',
      dbName: 'character_db', // 临时使用统一数据库
      options: {
        maxPoolSize: 15,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },

    // 球员服务数据库（支持分区分服）
    hero: {
      uri,
      dbName: serverId ? `hero_db_${serverId}` : 'hero_db',
      options: {
        maxPoolSize: 15,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },

    // 经济服务数据库（支持分区分服）
    economy: {
      uri,
      dbName: serverId ? `economy_db_${serverId}` : 'economy_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },

    // 社交服务数据库（支持分区分服）
    social: {
      uri,
      dbName: serverId ? `social_db_${serverId}` : 'social_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },

    // 活动服务数据库（支持分区分服）
    activity: {
      uri,
      dbName: serverId ? `activity_db_${serverId}` : 'activity_db',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },

    // 比赛服务数据库（支持分区分服）
    match: {
      uri,
      dbName: serverId ? `match_db_${serverId}` : 'match_db',
      options: {
        maxPoolSize: 15,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        retryWrites: true,
        retryReads: true,
      },
    },
  };

  const config = configs[serviceName];
  if (!config) {
    throw new Error(`Database configuration not found for service: ${serviceName}`);
  }

  return {
    uri: config.uri,
    dbName: config.dbName,
    ...config.options,
  };
};

// 数据库健康检查配置
export const createHealthCheckConfig = (configService: ConfigService, serviceName: string) => {
  const config = createMongoConfig(configService, serviceName);
  return {
    name: `${serviceName}_mongodb`,
    uri: config.uri,
    timeout: 5000,
  };
};

// 数据库连接事件处理
export const setupDatabaseEvents = (serviceName: string) => {
  return {
    connectionFactory: (connection: any) => {
      connection.on('connected', () => {
        console.log(`[${serviceName}] MongoDB connected successfully`);
      });

      connection.on('disconnected', () => {
        console.log(`[${serviceName}] MongoDB disconnected`);
      });

      connection.on('error', (error: any) => {
        console.error(`[${serviceName}] MongoDB connection error:`, error);
      });

      connection.on('reconnected', () => {
        console.log(`[${serviceName}] MongoDB reconnected`);
      });

      return connection;
    },
  };
};
