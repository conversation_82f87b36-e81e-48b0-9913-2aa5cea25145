import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GlobalServiceRegistryService, GlobalServiceInstance } from './global-service-registry.service';

/**
 * 全局服务自动注册服务
 * 
 * 为全局服务（如Auth、Payment等）提供自动注册功能
 * 特点：
 * - 零侵入性：业务代码无需修改
 * - 配置驱动：所有参数从环境变量获取
 * - 生命周期管理：自动注册和注销
 * - 心跳维护：定期更新服务状态
 */
@Injectable()
export class GlobalServiceAutoRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(GlobalServiceAutoRegistrationService.name);
  private instanceId: string | null = null;
  private heartbeatInterval: NodeJS.Timeout;

  constructor(
    @Inject('GLOBAL_SERVICE_CONFIG') private readonly config: {
      serviceName: string;
      weight: number;
      metadata: Record<string, any>;
      healthCheckPath: string;
    },
    private readonly configService: ConfigService,
    private readonly globalRegistry: GlobalServiceRegistryService,
  ) {}

  async onModuleInit() {
    await this.registerService();
    this.startHeartbeat();
  }

  async onModuleDestroy() {
    this.stopHeartbeat();
    if (this.instanceId) {
      await this.unregisterService();
    }
  }

  /**
   * 注册全局服务到注册中心
   */
  private async registerService(): Promise<void> {
    try {
      // 获取服务配置
      const host = this.getServiceHost();
      const port = this.getServicePort();
      
      // 构建服务实例信息
      const serviceInstance: GlobalServiceInstance = {
        serviceName: this.config.serviceName,
        host,
        port,
        weight: this.config.weight,
        metadata: {
          ...this.config.metadata,
          version: this.config.metadata.version || '1.0.0',
          environment: this.configService.get('NODE_ENV', 'development'),
          startTime: new Date().toISOString(),
          autoRegistered: true,
          registrationMode: 'ServiceRegistryModule.forGlobal',
          healthCheckPath: this.config.healthCheckPath,
        },
      };

      // 输出注册配置详情
      this.logger.log('🏷️ 全局服务自动注册配置详情:');
      this.logger.log(`   📋 服务名称: ${serviceInstance.serviceName}`);
      this.logger.log(`   🌍 服务类型: 全局服务`);
      this.logger.log(`   🌐 监听地址: ${serviceInstance.host}:${serviceInstance.port}`);
      this.logger.log(`   ⚖️ 负载权重: ${serviceInstance.weight}`);
      this.logger.log(`   🌍 运行环境: ${serviceInstance.metadata.environment}`);
      this.logger.log(`   🔧 自动注册: 启用`);

      // 注册服务实例
      this.instanceId = await this.globalRegistry.registerInstance(serviceInstance);

      this.logger.log('✅ 全局服务自动注册成功!');
      this.logger.log(`   🆔 实例ID: ${this.instanceId}`);
      this.logger.log(`   📍 注册地址: global_services:${serviceInstance.serviceName}`);
      this.logger.log(`   🔗 健康检查: http://${serviceInstance.host}:${serviceInstance.port}${this.config.healthCheckPath}`);
      this.logger.log(`   📊 元数据: ${JSON.stringify(this.config.metadata, null, 8)}`);
      this.logger.log(`🎉 ${this.config.serviceName} 服务已成功接入全局服务架构!`);

    } catch (error) {
      this.logger.error('❌ 全局服务自动注册失败:', error);
      // 注册失败不应该阻止服务启动
    }
  }

  /**
   * 注销服务
   */
  private async unregisterService(): Promise<void> {
    try {
      await this.globalRegistry.unregisterInstance(this.config.serviceName, this.instanceId);
      this.logger.log(`✅ 全局服务自动注销成功: ${this.instanceId}`);
    } catch (error) {
      this.logger.error('❌ 全局服务自动注销失败:', error);
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    const interval = this.configService.get('GLOBAL_SERVICE_HEARTBEAT_INTERVAL', 15000);
    
    this.heartbeatInterval = setInterval(async () => {
      if (this.instanceId) {
        try {
          await this.globalRegistry.updateHeartbeat(this.config.serviceName, this.instanceId);
        } catch (error) {
          this.logger.error('❌ 全局服务心跳更新失败:', error);
        }
      }
    }, interval);

    this.logger.debug(`💓 全局服务心跳启动，间隔: ${interval}ms`);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.logger.debug('💓 全局服务心跳停止');
    }
  }

  /**
   * 获取服务主机地址
   */
  private getServiceHost(): string {
    return this.configService.get('SERVICE_HOST', '127.0.0.1');
  }

  /**
   * 获取服务端口
   */
  private getServicePort(): number {
    const serviceName = this.config.serviceName.toUpperCase();
    
    // 按优先级获取端口
    const port = 
      this.configService.get(`${serviceName}_PORT`) ||
      this.configService.get('PORT') ||
      this.getDefaultPort(this.config.serviceName);

    return parseInt(port, 10);
  }

  /**
   * 获取默认端口
   */
  private getDefaultPort(serviceName: string): number {
    const defaultPorts: Record<string, number> = {
      gateway: 3000,
      auth: 3001,
      character: 3002,
      hero: 3003,
      economy: 3004,
      activity: 3005,
      social: 3006,
      match: 3007,
      notification: 3008,
      payment: 3009,
      analytics: 3010,
    };

    return defaultPorts[serviceName] || 3000;
  }

  /**
   * 获取注册状态
   */
  getRegistrationStatus(): {
    registered: boolean;
    serviceName: string;
    instanceId: string | null;
    serviceType: 'global';
  } {
    return {
      registered: !!this.instanceId,
      serviceName: this.config.serviceName,
      instanceId: this.instanceId,
      serviceType: 'global',
    };
  }
}
