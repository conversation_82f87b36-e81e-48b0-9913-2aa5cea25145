import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { ServiceInstance } from './unified-service-discovery.service';

/**
 * 全局服务注册中心
 * 
 * 管理跨区服的全局服务，如Auth、Payment等
 * 特点：
 * - 数据全局共享
 * - 支持全局负载均衡
 * - 健康检查和故障转移
 * - 自动实例清理
 */
@Injectable()
export class GlobalServiceRegistryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(GlobalServiceRegistryService.name);
  private healthCheckInterval: NodeJS.Timeout;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    this.logger.log('🌍 全局服务注册中心启动');
    this.startHealthCheck();
  }

  async onModuleDestroy() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.logger.log('🌍 全局服务注册中心关闭');
  }

  /**
   * 注册全局服务实例
   */
  async registerInstance(instance: GlobalServiceInstance): Promise<string> {
    const instanceId = `${instance.serviceName}-global-${Date.now()}`;
    const key = this.getServiceKey(instance.serviceName);
    
    const instanceData: StoredGlobalServiceInstance = {
      ...instance,
      instanceId,
      serviceType: 'global',
      registeredAt: new Date().toISOString(),
      lastHeartbeat: new Date().toISOString(),
      healthy: true,
    };

    // 🔧 修复：使用正确的数据类型存储全局服务实例，RedisService会自动序列化
    await this.redisService.hset(key, instanceId, instanceData, 'global');
    
    this.logger.log(`✅ 全局服务注册成功: ${instance.serviceName} (${instanceId})`);
    this.logger.log(`   📍 注册地址: ${key}`);
    this.logger.log(`   🔗 服务地址: http://${instance.host}:${instance.port}`);
    
    return instanceId;
  }

  /**
   * 获取健康的服务实例
   */
  async getHealthyInstances(serviceName: string): Promise<StoredGlobalServiceInstance[]> {
    const key = this.getServiceKey(serviceName);
    // 🔧 修复：使用正确的数据类型获取全局服务实例
    const instances = await this.redisService.hgetall(key, 'global');

    if (!instances || Object.keys(instances).length === 0) {
      this.logger.warn(`⚠️ 没有找到全局服务实例: ${serviceName}`);
      return [];
    }

    // 🔧 关键修复：RedisService已经自动反序列化，不需要再次JSON.parse
    const healthyInstances = Object.values(instances)
      .map(data => data as StoredGlobalServiceInstance) // 直接类型转换，不需要JSON.parse
      .filter(instance => instance.healthy);

    this.logger.debug(`🔍 全局服务 ${serviceName}: 总实例=${Object.keys(instances).length}, 健康实例=${healthyInstances.length}`);

    return healthyInstances;
  }

  /**
   * 选择服务实例（全局负载均衡）
   */
  async selectInstance(serviceName: string, strategy: string = 'round-robin'): Promise<ServiceInstance> {
    const instances = await this.getHealthyInstances(serviceName);
    
    if (instances.length === 0) {
      throw new Error(`全局服务 ${serviceName} 无可用实例`);
    }

    // 简单轮询负载均衡
    const selectedIndex = Math.floor(Math.random() * instances.length);
    const selected = instances[selectedIndex];
    
    this.logger.debug(`✅ 选择全局服务实例: ${selected.instanceId} (${selected.host}:${selected.port})`);
    
    return {
      id: selected.instanceId,
      serviceName: selected.serviceName,
      host: selected.host,
      port: selected.port,
      healthy: selected.healthy,
      weight: selected.weight,
      metadata: selected.metadata,
    };
  }

  /**
   * 注销服务实例
   */
  async unregisterInstance(serviceName: string, instanceId: string): Promise<void> {
    const key = this.getServiceKey(serviceName);
    // 🔧 修复：使用正确的数据类型删除全局服务实例
    const result = await this.redisService.hdel(key, [instanceId], 'global');
    
    if (result > 0) {
      this.logger.log(`✅ 全局服务注销成功: ${serviceName} (${instanceId})`);
    } else {
      this.logger.warn(`⚠️ 全局服务注销失败，实例不存在: ${serviceName} (${instanceId})`);
    }
  }

  /**
   * 更新实例心跳
   */
  async updateHeartbeat(serviceName: string, instanceId: string): Promise<void> {
    const key = this.getServiceKey(serviceName);
    // 🔧 修复：使用正确的数据类型获取实例数据
    const instanceData = await this.redisService.hget(key, instanceId, 'global');

    if (instanceData) {
      // 🔧 关键修复：RedisService已经自动反序列化，不需要再次JSON.parse
      const instance = instanceData as StoredGlobalServiceInstance;
      instance.lastHeartbeat = new Date().toISOString();
      instance.healthy = true;

      // 🔧 修复：使用正确的数据类型更新心跳，RedisService会自动序列化
      await this.redisService.hset(key, instanceId, instance, 'global');
      this.logger.debug(`💓 更新全局服务心跳: ${serviceName} (${instanceId})`);
    }
  }

  /**
   * 获取服务键名 - 使用正确的全局数据类型
   */
  private getServiceKey(serviceName: string): string {
    // 🔧 修复：使用RedisService的buildDataTypeKey方法构造正确的全局键
    return this.redisService.buildDataTypeKey(`global_services:${serviceName}`, 'global');
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    const interval = this.configService.get('GLOBAL_SERVICE_HEALTH_CHECK_INTERVAL', 30000);
    
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, interval);
    
    this.logger.log(`🏥 全局服务健康检查启动，间隔: ${interval}ms`);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const timeout = this.configService.get('GLOBAL_SERVICE_INSTANCE_TIMEOUT', 90000);
      const now = new Date().getTime();
      let totalInstances = 0;
      let healthyInstances = 0;
      let removedInstances = 0;

      // 🔧 修复：使用正确的数据类型搜索全局服务键
      const serviceKeys = await this.redisService.keys('global_services:*', 'global');

      for (const businessKey of serviceKeys) {
        // 🔧 修复：使用正确的数据类型获取哈希数据
        const instances = await this.redisService.hgetall(businessKey, 'global');
        
        for (const [instanceId, instanceData] of Object.entries(instances)) {
          totalInstances++;
          // 🔧 关键修复：RedisService已经自动反序列化，不需要再次JSON.parse
          const instance = instanceData as StoredGlobalServiceInstance;
          const lastHeartbeat = new Date(instance.lastHeartbeat).getTime();

          if (now - lastHeartbeat > timeout) {
            // 🔧 修复：使用正确的数据类型删除超时实例
            await this.redisService.hdel(businessKey, [instanceId], 'global');
            removedInstances++;
            this.logger.warn(`⏰ 移除超时的全局服务实例: ${instance.serviceName} (${instanceId})`);
          } else {
            healthyInstances++;
          }
        }
      }

      this.logger.debug(`🧹 全局服务健康检查完成: 总实例=${totalInstances}, 健康=${healthyInstances}, 超时移除=${removedInstances}`);
    } catch (error) {
      this.logger.error('❌ 全局服务健康检查失败:', error);
    }
  }
}

/**
 * 全局服务实例接口
 */
export interface GlobalServiceInstance {
  serviceName: string;
  host: string;
  port: number;
  weight?: number;
  metadata?: Record<string, any>;
}

/**
 * 存储的全局服务实例接口
 */
export interface StoredGlobalServiceInstance extends GlobalServiceInstance {
  instanceId: string;
  serviceType: 'global';
  registeredAt: string;
  lastHeartbeat: string;
  healthy: boolean;
}

// ServiceInstance接口已在unified-service-discovery.service.ts中定义
