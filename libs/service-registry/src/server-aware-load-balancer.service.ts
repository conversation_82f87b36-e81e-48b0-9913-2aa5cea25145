import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerAwareRegistryService, ServerAwareServiceInstance } from './server-aware-registry.service';

/**
 * 负载均衡策略接口
 */
export interface LoadBalancingStrategy {
  name: string;
  selectInstance(instances: ServerAwareServiceInstance[], context?: any): ServerAwareServiceInstance | null;
}

/**
 * 负载均衡上下文
 */
export interface LoadBalancingContext {
  ip?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  [key: string]: any;
}

/**
 * 区服感知的负载均衡器
 * 
 * 核心功能：
 * - 为指定区服选择最佳服务实例
 * - 支持多种负载均衡策略
 * - 自动故障转移和健康检查
 * - 连接数和响应时间统计
 */
@Injectable()
export class ServerAwareLoadBalancerService {
  private readonly logger = new Logger(ServerAwareLoadBalancerService.name);
  
  // 负载均衡策略
  private readonly strategies = new Map<string, LoadBalancingStrategy>();
  
  // 轮询计数器：Map<serviceName-serverId, counter>
  private readonly roundRobinCounters = new Map<string, number>();
  
  // 默认策略
  private readonly defaultStrategy: string;

  constructor(
    private readonly serviceRegistry: ServerAwareRegistryService,
    private readonly configService: ConfigService,
  ) {
    this.defaultStrategy = this.configService.get<string>('gateway.loadBalancer.defaultStrategy', 'round-robin');
    this.initializeStrategies();
    
    this.logger.log(`🎯 区服感知负载均衡器启动，默认策略: ${this.defaultStrategy}`);
  }

  /**
   * 为指定区服选择服务实例
   */
  async selectInstance(
    serviceName: string,
    serverId: string,
    strategy: string = this.defaultStrategy,
    context?: LoadBalancingContext
  ): Promise<ServerAwareServiceInstance | null> {
    // 获取健康的服务实例
    const instances = await this.serviceRegistry.getHealthyInstances(serviceName, serverId);

    if (instances.length === 0) {
      this.logger.warn(`❌ 没有可用的健康实例: ${serviceName}@${serverId}`);
      return null;
    }
    
    // 获取负载均衡策略
    const loadBalancer = this.strategies.get(strategy);
    if (!loadBalancer) {
      this.logger.warn(`⚠️ 未知的负载均衡策略: ${strategy}，使用默认策略: ${this.defaultStrategy}`);
      return this.selectInstance(serviceName, serverId, this.defaultStrategy, context);
    }
    
    // 选择实例
    const selectedInstance = loadBalancer.selectInstance(instances, context);
    
    if (selectedInstance) {
      // 增加连接数
      this.serviceRegistry.incrementConnections(serviceName, serverId, selectedInstance.instanceName);
      
      this.logger.debug(`🎯 选择实例: ${selectedInstance.instanceName} (${serviceName}@${serverId}) 策略=${strategy}`);
    }
    
    return selectedInstance;
  }

  /**
   * 释放实例连接
   */
  releaseInstance(serviceName: string, serverId: string, instanceName: string): void {
    this.serviceRegistry.decrementConnections(serviceName, serverId, instanceName);
    this.logger.debug(`🔓 释放实例连接: ${instanceName} (${serviceName}@${serverId})`);
  }

  /**
   * 获取可用的负载均衡策略列表
   */
  getAvailableStrategies(): string[] {
    return Array.from(this.strategies.keys());
  }

  /**
   * 获取负载均衡统计信息
   */
  getLoadBalancingStats(serviceName?: string, serverId?: string): any {
    if (serviceName && serverId) {
      return this.getServerStats(serviceName, serverId);
    } else if (serviceName) {
      return this.getServiceStats(serviceName);
    } else {
      return this.getAllStats();
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化负载均衡策略
   */
  private initializeStrategies(): void {
    // 轮询策略
    this.strategies.set('round-robin', {
      name: 'round-robin',
      selectInstance: (instances: ServerAwareServiceInstance[], context?: any) => {
        if (instances.length === 0) return null;
        
        // 使用服务名和区服ID作为计数器键
        const counterKey = context?.counterKey || 'default';
        const counter = this.roundRobinCounters.get(counterKey) || 0;
        const selectedInstance = instances[counter % instances.length];
        
        this.roundRobinCounters.set(counterKey, counter + 1);
        return selectedInstance;
      },
    });

    // 加权轮询策略
    this.strategies.set('weighted-round-robin', {
      name: 'weighted-round-robin',
      selectInstance: (instances: ServerAwareServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        const totalWeight = instances.reduce((sum, inst) => sum + inst.weight, 0);
        if (totalWeight === 0) return instances[0];
        
        const random = Math.random() * totalWeight;
        let currentWeight = 0;
        
        for (const instance of instances) {
          currentWeight += instance.weight;
          if (random <= currentWeight) {
            return instance;
          }
        }
        
        return instances[instances.length - 1];
      },
    });

    // 最少连接策略
    this.strategies.set('least-connections', {
      name: 'least-connections',
      selectInstance: (instances: ServerAwareServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        return instances.reduce((min, current) => 
          current.connections < min.connections ? current : min
        );
      },
    });

    // IP哈希策略
    this.strategies.set('ip-hash', {
      name: 'ip-hash',
      selectInstance: (instances: ServerAwareServiceInstance[], context?: LoadBalancingContext) => {
        if (instances.length === 0) return null;
        
        const ip = context?.ip || '127.0.0.1';
        const hash = this.hashString(ip);
        const index = hash % instances.length;
        
        return instances[index];
      },
    });

    // 用户哈希策略（用于会话保持）
    this.strategies.set('user-hash', {
      name: 'user-hash',
      selectInstance: (instances: ServerAwareServiceInstance[], context?: LoadBalancingContext) => {
        if (instances.length === 0) return null;
        
        const userId = context?.userId || context?.sessionId || '0';
        const hash = this.hashString(userId);
        const index = hash % instances.length;
        
        return instances[index];
      },
    });

    // 响应时间策略
    this.strategies.set('response-time', {
      name: 'response-time',
      selectInstance: (instances: ServerAwareServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        // 按响应时间排序，选择响应时间最短的
        return instances.reduce((min, current) => 
          current.responseTime < min.responseTime ? current : min
        );
      },
    });

    // 健康度策略（综合响应时间和连接数）
    this.strategies.set('health-score', {
      name: 'health-score',
      selectInstance: (instances: ServerAwareServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        // 计算健康度分数（响应时间 + 连接数 * 权重）
        const scoredInstances = instances.map(instance => ({
          instance,
          score: instance.responseTime + (instance.connections * 10) - (instance.weight * 5),
        }));
        
        // 选择分数最低的（最健康的）
        const best = scoredInstances.reduce((min, current) => 
          current.score < min.score ? current : min
        );
        
        return best.instance;
      },
    });

    // 随机策略
    this.strategies.set('random', {
      name: 'random',
      selectInstance: (instances: ServerAwareServiceInstance[]) => {
        if (instances.length === 0) return null;
        
        const randomIndex = Math.floor(Math.random() * instances.length);
        return instances[randomIndex];
      },
    });

    this.logger.log(`📋 已初始化 ${this.strategies.size} 种负载均衡策略: ${Array.from(this.strategies.keys()).join(', ')}`);
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 获取单个区服的统计信息
   */
  private getServerStats(serviceName: string, serverId: string): any {
    const instances = this.serviceRegistry.getInstances(serviceName, serverId);
    const healthyInstances = instances.filter(inst => inst.healthy);
    
    const totalConnections = instances.reduce((sum, inst) => sum + inst.connections, 0);
    const avgResponseTime = instances.length > 0 
      ? instances.reduce((sum, inst) => sum + inst.responseTime, 0) / instances.length 
      : 0;
    
    return {
      serviceName,
      serverId,
      totalInstances: instances.length,
      healthyInstances: healthyInstances.length,
      totalConnections,
      avgResponseTime: Math.round(avgResponseTime),
      instances: instances.map(inst => ({
        instanceName: inst.instanceName,
        healthy: inst.healthy,
        connections: inst.connections,
        responseTime: inst.responseTime,
        weight: inst.weight,
        lastHealthCheck: inst.lastHealthCheck,
      })),
    };
  }

  /**
   * 获取单个服务的统计信息
   */
  private getServiceStats(serviceName: string): any {
    const allInstances = this.serviceRegistry.getAllInstances(serviceName);
    const servers: any = {};
    
    let totalInstances = 0;
    let totalHealthyInstances = 0;
    let totalConnections = 0;
    let totalResponseTime = 0;
    
    for (const [serverId, instances] of allInstances) {
      const healthyInstances = instances.filter(inst => inst.healthy);
      const serverConnections = instances.reduce((sum, inst) => sum + inst.connections, 0);
      const serverResponseTime = instances.length > 0 
        ? instances.reduce((sum, inst) => sum + inst.responseTime, 0) / instances.length 
        : 0;
      
      servers[serverId] = {
        totalInstances: instances.length,
        healthyInstances: healthyInstances.length,
        totalConnections: serverConnections,
        avgResponseTime: Math.round(serverResponseTime),
      };
      
      totalInstances += instances.length;
      totalHealthyInstances += healthyInstances.length;
      totalConnections += serverConnections;
      totalResponseTime += serverResponseTime;
    }
    
    return {
      serviceName,
      totalServers: allInstances.size,
      totalInstances,
      totalHealthyInstances,
      totalConnections,
      avgResponseTime: allInstances.size > 0 ? Math.round(totalResponseTime / allInstances.size) : 0,
      servers,
    };
  }

  /**
   * 获取所有服务的统计信息
   */
  private getAllStats(): any {
    const serviceStats = this.serviceRegistry.getServiceStats();
    const strategies = this.getAvailableStrategies();
    
    return {
      defaultStrategy: this.defaultStrategy,
      availableStrategies: strategies,
      services: serviceStats,
    };
  }
}
