import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisService } from '@common/redis';

/**
 * 区服感知的服务实例接口
 */
export interface ServerAwareServiceInstance {
  id: string;                    // 实例唯一ID
  serviceName: string;           // 服务名称（如character、hero）
  serverId: string;              // 区服ID（如server001）
  instanceName: string;          // 实例名称（如character-server001-1）
  host: string;                  // 主机地址
  port: number;                  // 端口号
  healthy: boolean;              // 健康状态
  weight: number;                // 负载均衡权重
  connections: number;           // 当前连接数
  responseTime: number;          // 平均响应时间
  lastHealthCheck: Date;         // 最后健康检查时间
  registeredAt: Date;            // 注册时间
  metadata: Record<string, any>; // 元数据
}

/**
 * 服务注册请求接口
 */
export interface ServiceRegistrationRequest {
  serviceName: string;
  serverId: string;
  instanceName: string;
  host: string;
  port: number;
  healthy?: boolean;
  weight?: number;
  metadata?: Record<string, any>;
}

/**
 * 区服感知的服务注册中心
 * 
 * 核心功能：
 * - 管理各区服的服务实例注册
 * - 提供区服级别的服务发现
 * - 支持区服级别的负载均衡
 * - 健康检查和故障转移
 */
@Injectable()
export class ServerAwareRegistryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServerAwareRegistryService.name);
  
  // 服务实例存储：Map<serviceName, Map<serverId, ServiceInstance[]>>
  private readonly instances = new Map<string, Map<string, ServerAwareServiceInstance[]>>();
  
  // 健康检查定时器
  private healthCheckInterval: NodeJS.Timeout | null = null;
  
  // 配置
  private readonly healthCheckIntervalMs: number;
  private readonly instanceTimeoutMs: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    @Inject('RedisService_service-registry') private readonly redisService: RedisService,
  ) {
    this.healthCheckIntervalMs = this.configService.get<number>('gateway.serviceRegistry.healthCheckInterval', 30000);
    this.instanceTimeoutMs = this.configService.get<number>('gateway.serviceRegistry.instanceTimeout', 90000);
  }

  async onModuleInit() {
    this.logger.log('🚀 区服感知服务注册中心启动');
    
    // 启动健康检查
    this.startHealthCheck();
    
    // 输出配置信息
    this.logger.log(`健康检查间隔: ${this.healthCheckIntervalMs}ms`);
    this.logger.log(`实例超时时间: ${this.instanceTimeoutMs}ms`);
  }

  async onModuleDestroy() {
    this.logger.log('🛑 区服感知服务注册中心关闭');
    
    // 停止健康检查
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }

  /**
   * 注册服务实例
   */
  async registerInstance(request: ServiceRegistrationRequest): Promise<string> {
    const { serviceName, serverId, instanceName, host, port } = request;
    
    // 生成实例ID
    const instanceId = `${instanceName}-${Date.now()}`;
    
    // 创建服务实例
    const instance: ServerAwareServiceInstance = {
      id: instanceId,
      serviceName,
      serverId,
      instanceName,
      host,
      port,
      healthy: request.healthy ?? true,
      weight: request.weight ?? 1,
      connections: 0,
      responseTime: 0,
      lastHealthCheck: new Date(),
      registeredAt: new Date(),
      metadata: request.metadata ?? {},
    };
    
    // 确保服务映射存在
    if (!this.instances.has(serviceName)) {
      this.instances.set(serviceName, new Map());
    }
    
    const serviceMap = this.instances.get(serviceName)!;
    
    // 确保区服映射存在
    if (!serviceMap.has(serverId)) {
      serviceMap.set(serverId, []);
    }
    
    const serverInstances = serviceMap.get(serverId)!;
    
    // 检查是否已存在相同实例名的实例
    const existingIndex = serverInstances.findIndex(inst => inst.instanceName === instanceName);
    if (existingIndex >= 0) {
      // 更新现有实例
      serverInstances[existingIndex] = instance;
      this.logger.log(`🔄 更新服务实例: ${instanceName} (${serviceName}@${serverId})`);
    } else {
      // 添加新实例
      serverInstances.push(instance);
      this.logger.log(`✅ 注册服务实例: ${instanceName} (${serviceName}@${serverId})`);
    }

    // 同步到Redis分布式注册中心
    try {
      // 使用固定的Redis键前缀，不依赖于服务的Redis前缀
      const redisKey = `global:service_registry:instances:${serviceName}:${serverId}`;
      const instanceData = JSON.stringify(instance);

      // 使用Redis Hash存储实例信息
      await this.redisService.hset(redisKey, instanceName, instanceData);

      // 设置过期时间（5分钟）
      await this.redisService.expire(redisKey, 300);

      // 发布服务注册事件
      await this.redisService.publish('global:service_registry:events', JSON.stringify({
        type: 'instance_registered',
        serviceName,
        serverId,
        instanceName,
        instanceId,
        timestamp: new Date().toISOString(),
      }));

      this.logger.debug(`📡 已同步实例到Redis: ${instanceName}`);
    } catch (redisError) {
      this.logger.warn(`⚠️ Redis同步失败: ${redisError.message}`);
    }

    // 发出注册事件
    this.eventEmitter.emit('service.instance.registered', {
      serviceName,
      serverId,
      instance,
    });

    return instanceId;
  }

  /**
   * 注销服务实例
   */
  unregisterInstance(serviceName: string, serverId: string, instanceName: string): boolean {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return false;
    
    const serverInstances = serviceMap.get(serverId);
    if (!serverInstances) return false;
    
    const index = serverInstances.findIndex(inst => inst.instanceName === instanceName);
    if (index === -1) return false;
    
    const removedInstance = serverInstances.splice(index, 1)[0];
    
    this.logger.log(`🗑️ 注销服务实例: ${instanceName} (${serviceName}@${serverId})`);
    
    // 发出注销事件
    this.eventEmitter.emit('service.instance.unregistered', {
      serviceName,
      serverId,
      instance: removedInstance,
    });
    
    return true;
  }

  /**
   * 获取指定区服的服务实例
   */
  getInstances(serviceName: string, serverId: string): ServerAwareServiceInstance[] {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return [];

    const serverInstances = serviceMap.get(serverId);
    return serverInstances ? [...serverInstances] : [];
  }

  /**
   * 获取指定区服的健康服务实例
   */
  async getHealthyInstances(serviceName: string, serverId: string): Promise<ServerAwareServiceInstance[]> {
    // 首先尝试从Redis获取最新的实例信息
    try {
      // 使用固定的Redis键前缀，不依赖于服务的Redis前缀
      const redisKey = `global:service_registry:instances:${serviceName}:${serverId}`;
      const instancesData = await this.redisService.hgetall(redisKey);

      if (instancesData && Object.keys(instancesData).length > 0) {
        const redisInstances: ServerAwareServiceInstance[] = [];

        for (const [instanceName, instanceDataStr] of Object.entries(instancesData)) {
          try {
            const instanceData = JSON.parse(instanceDataStr as string);
            // 转换日期字符串为Date对象
            instanceData.lastHealthCheck = new Date(instanceData.lastHealthCheck);
            instanceData.registeredAt = new Date(instanceData.registeredAt);
            redisInstances.push(instanceData);
          } catch (parseError) {
            this.logger.warn(`⚠️ 解析Redis实例数据失败: ${instanceName}`);
          }
        }

        // 返回健康的实例
        const healthyInstances = redisInstances.filter(instance => instance.healthy);
        this.logger.debug(`📡 从Redis获取健康实例: ${serviceName}@${serverId} (${healthyInstances.length}/${redisInstances.length})`);
        return healthyInstances;
      }
    } catch (redisError) {
      this.logger.warn(`⚠️ 从Redis获取实例失败: ${redisError.message}`);
    }

    // 如果Redis获取失败，回退到内存存储
    const instances = this.getInstances(serviceName, serverId);
    return instances.filter(instance => instance.healthy);
  }

  /**
   * 获取所有区服的服务实例
   */
  getAllInstances(serviceName: string): Map<string, ServerAwareServiceInstance[]> {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return new Map();
    
    const result = new Map<string, ServerAwareServiceInstance[]>();
    for (const [serverId, instances] of serviceMap) {
      result.set(serverId, [...instances]);
    }
    
    return result;
  }

  /**
   * 获取可用的区服列表
   */
  getAvailableServers(serviceName: string): string[] {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) return [];
    
    const availableServers: string[] = [];
    for (const [serverId, instances] of serviceMap) {
      const healthyInstances = instances.filter(inst => inst.healthy);
      if (healthyInstances.length > 0) {
        availableServers.push(serverId);
      }
    }
    
    return availableServers;
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats(serviceName?: string): any {
    if (serviceName) {
      return this.getServiceStatsByName(serviceName);
    }
    
    const stats: any = {};
    for (const [name] of this.instances) {
      stats[name] = this.getServiceStatsByName(name);
    }
    
    return stats;
  }

  /**
   * 更新实例健康状态
   */
  updateInstanceHealth(serviceName: string, serverId: string, instanceName: string, healthy: boolean, responseTime?: number): boolean {
    const instances = this.getInstances(serviceName, serverId);
    const instance = instances.find(inst => inst.instanceName === instanceName);
    
    if (!instance) return false;
    
    const wasHealthy = instance.healthy;
    instance.healthy = healthy;
    instance.lastHealthCheck = new Date();
    
    if (responseTime !== undefined) {
      instance.responseTime = responseTime;
    }
    
    // 如果健康状态发生变化，发出事件
    if (wasHealthy !== healthy) {
      this.eventEmitter.emit('service.instance.health.changed', {
        serviceName,
        serverId,
        instanceName,
        healthy,
        instance,
      });
      
      this.logger.log(`🏥 实例健康状态变更: ${instanceName} (${serviceName}@${serverId}) -> ${healthy ? '健康' : '不健康'}`);
    }
    
    return true;
  }

  /**
   * 增加实例连接数
   */
  incrementConnections(serviceName: string, serverId: string, instanceName: string): boolean {
    const instances = this.getInstances(serviceName, serverId);
    const instance = instances.find(inst => inst.instanceName === instanceName);
    
    if (!instance) return false;
    
    instance.connections++;
    return true;
  }

  /**
   * 减少实例连接数
   */
  decrementConnections(serviceName: string, serverId: string, instanceName: string): boolean {
    const instances = this.getInstances(serviceName, serverId);
    const instance = instances.find(inst => inst.instanceName === instanceName);
    
    if (!instance) return false;
    
    instance.connections = Math.max(0, instance.connections - 1);
    return true;
  }

  // ==================== 私有方法 ====================

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckIntervalMs);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const now = new Date();
    let totalInstances = 0;
    let healthyInstances = 0;
    let timeoutInstances = 0;
    
    for (const [serviceName, serviceMap] of this.instances) {
      for (const [serverId, instances] of serviceMap) {
        for (let i = instances.length - 1; i >= 0; i--) {
          const instance = instances[i];
          totalInstances++;
          
          // 检查实例是否超时
          const timeSinceLastCheck = now.getTime() - instance.lastHealthCheck.getTime();
          if (timeSinceLastCheck > this.instanceTimeoutMs) {
            // 移除超时实例
            instances.splice(i, 1);
            timeoutInstances++;
            
            this.logger.warn(`⏰ 移除超时实例: ${instance.instanceName} (${serviceName}@${serverId})`);
            
            this.eventEmitter.emit('service.instance.timeout', {
              serviceName,
              serverId,
              instance,
            });
          } else if (instance.healthy) {
            healthyInstances++;
          }
        }
      }
    }
    
    if (timeoutInstances > 0) {
      this.logger.log(`🧹 健康检查完成: 总实例=${totalInstances}, 健康=${healthyInstances}, 超时移除=${timeoutInstances}`);
    }
  }

  /**
   * 获取单个服务的统计信息
   */
  private getServiceStatsByName(serviceName: string): any {
    const serviceMap = this.instances.get(serviceName);
    if (!serviceMap) {
      return {
        totalServers: 0,
        totalInstances: 0,
        healthyInstances: 0,
        servers: {},
      };
    }
    
    let totalInstances = 0;
    let healthyInstances = 0;
    const servers: any = {};
    
    for (const [serverId, instances] of serviceMap) {
      const serverHealthyInstances = instances.filter(inst => inst.healthy).length;
      
      servers[serverId] = {
        totalInstances: instances.length,
        healthyInstances: serverHealthyInstances,
        instances: instances.map(inst => ({
          instanceName: inst.instanceName,
          healthy: inst.healthy,
          connections: inst.connections,
          responseTime: inst.responseTime,
          lastHealthCheck: inst.lastHealthCheck,
        })),
      };
      
      totalInstances += instances.length;
      healthyInstances += serverHealthyInstances;
    }
    
    return {
      totalServers: serviceMap.size,
      totalInstances,
      healthyInstances,
      servers,
    };
  }
}
