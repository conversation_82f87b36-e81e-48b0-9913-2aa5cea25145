import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ServerAwareRegistryService, ServerAwareServiceInstance } from './server-aware-registry.service';

/**
 * 实例生命周期事件接口
 */
export interface InstanceLifecycleEvent {
  type: 'starting' | 'started' | 'stopping' | 'stopped' | 'failed' | 'recovered';
  instance: ServerAwareServiceInstance;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * 实例部署配置接口
 */
export interface InstanceDeploymentConfig {
  serviceName: string;
  serverId: string;
  instanceIndex: number;
  port: number;
  host?: string;
  environment?: Record<string, string>;
  resources?: {
    cpu?: string;
    memory?: string;
    disk?: string;
  };
  healthCheck?: {
    path?: string;
    interval?: number;
    timeout?: number;
    retries?: number;
  };
}

/**
 * 实例状态接口
 */
export interface InstanceStatus {
  instanceId: string;
  status: 'pending' | 'starting' | 'running' | 'stopping' | 'stopped' | 'failed';
  health: 'healthy' | 'unhealthy' | 'unknown';
  startTime?: Date;
  lastHealthCheck?: Date;
  restartCount: number;
  metadata: Record<string, any>;
}

/**
 * 微服务实例生命周期管理服务
 * 
 * 核心功能：
 * - 实例启动和停止管理
 * - 健康检查和自动重启
 * - 实例状态跟踪和事件通知
 * - 优雅关闭和资源清理
 * - 实例扩缩容管理
 */
@Injectable()
export class InstanceLifecycleService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(InstanceLifecycleService.name);
  
  // 实例状态跟踪：Map<instanceId, InstanceStatus>
  private readonly instanceStatuses = new Map<string, InstanceStatus>();
  
  // 健康检查定时器：Map<instanceId, NodeJS.Timeout>
  private readonly healthCheckTimers = new Map<string, NodeJS.Timeout>();
  
  // 重启队列：Set<instanceId>
  private readonly restartQueue = new Set<string>();
  
  // 配置
  private readonly maxRestartAttempts: number;
  private readonly restartDelay: number;
  private readonly healthCheckInterval: number;
  private readonly gracefulShutdownTimeout: number;

  constructor(
    private readonly serviceRegistry: ServerAwareRegistryService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
  ) {
    this.maxRestartAttempts = this.configService.get<number>('instanceLifecycle.maxRestartAttempts', 3);
    this.restartDelay = this.configService.get<number>('instanceLifecycle.restartDelay', 5000);
    this.healthCheckInterval = this.configService.get<number>('instanceLifecycle.healthCheckInterval', 30000);
    this.gracefulShutdownTimeout = this.configService.get<number>('instanceLifecycle.gracefulShutdownTimeout', 30000);
  }

  async onModuleInit() {
    this.logger.log('🚀 实例生命周期管理服务启动');
    
    // 监听服务注册中心事件
    this.eventEmitter.on('service.instance.registered', this.handleInstanceRegistered.bind(this));
    this.eventEmitter.on('service.instance.unregistered', this.handleInstanceUnregistered.bind(this));
    this.eventEmitter.on('service.instance.health.changed', this.handleInstanceHealthChanged.bind(this));
    
    this.logger.log(`配置: 最大重启次数=${this.maxRestartAttempts}, 重启延迟=${this.restartDelay}ms`);
  }

  async onModuleDestroy() {
    this.logger.log('🛑 实例生命周期管理服务关闭');
    
    // 停止所有健康检查
    for (const timer of this.healthCheckTimers.values()) {
      clearInterval(timer);
    }
    this.healthCheckTimers.clear();
    
    // 优雅关闭所有实例
    await this.shutdownAllInstances();
  }

  /**
   * 启动服务实例
   */
  async startInstance(config: InstanceDeploymentConfig): Promise<string> {
    const instanceId = `${config.serviceName}-${config.serverId}-${config.instanceIndex}`;
    
    this.logger.log(`🚀 启动实例: ${instanceId}`);
    
    // 创建实例状态
    const status: InstanceStatus = {
      instanceId,
      status: 'starting',
      health: 'unknown',
      restartCount: 0,
      metadata: {
        serviceName: config.serviceName,
        serverId: config.serverId,
        instanceIndex: config.instanceIndex,
        port: config.port,
        host: config.host || 'localhost',
        startTime: new Date().toISOString(),
      },
    };
    
    this.instanceStatuses.set(instanceId, status);
    
    try {
      // 发出启动事件
      await this.emitLifecycleEvent('starting', {
        id: instanceId,
        serviceName: config.serviceName,
        serverId: config.serverId,
        instanceName: instanceId,
        host: config.host || 'localhost',
        port: config.port,
        healthy: false,
        weight: 1,
        connections: 0,
        responseTime: 0,
        lastHealthCheck: new Date(),
        registeredAt: new Date(),
        metadata: status.metadata,
      });
      
      // 执行实际的实例启动逻辑
      await this.performInstanceStart(config);
      
      // 更新状态为运行中
      status.status = 'running';
      status.startTime = new Date();
      
      // 注册到服务注册中心
      const registeredInstanceId = await this.serviceRegistry.registerInstance({
        serviceName: config.serviceName,
        serverId: config.serverId,
        instanceName: instanceId,
        host: config.host || 'localhost',
        port: config.port,
        healthy: true,
        metadata: status.metadata,
      });
      
      // 启动健康检查
      this.startHealthCheck(instanceId, config);
      
      // 发出启动完成事件
      await this.emitLifecycleEvent('started', {
        id: registeredInstanceId,
        serviceName: config.serviceName,
        serverId: config.serverId,
        instanceName: instanceId,
        host: config.host || 'localhost',
        port: config.port,
        healthy: true,
        weight: 1,
        connections: 0,
        responseTime: 0,
        lastHealthCheck: new Date(),
        registeredAt: new Date(),
        metadata: status.metadata,
      });
      
      this.logger.log(`✅ 实例启动成功: ${instanceId}`);
      return registeredInstanceId;
      
    } catch (error) {
      this.logger.error(`❌ 实例启动失败: ${instanceId}`, error);
      
      // 更新状态为失败
      status.status = 'failed';
      
      // 发出失败事件
      await this.emitLifecycleEvent('failed', {
        id: instanceId,
        serviceName: config.serviceName,
        serverId: config.serverId,
        instanceName: instanceId,
        host: config.host || 'localhost',
        port: config.port,
        healthy: false,
        weight: 1,
        connections: 0,
        responseTime: 0,
        lastHealthCheck: new Date(),
        registeredAt: new Date(),
        metadata: { ...status.metadata, error: error.message },
      });
      
      throw error;
    }
  }

  /**
   * 停止服务实例
   */
  async stopInstance(instanceId: string, graceful: boolean = true): Promise<void> {
    const status = this.instanceStatuses.get(instanceId);
    if (!status) {
      throw new Error(`实例不存在: ${instanceId}`);
    }
    
    this.logger.log(`🛑 停止实例: ${instanceId}, 优雅关闭=${graceful}`);
    
    // 更新状态为停止中
    status.status = 'stopping';
    
    try {
      // 停止健康检查
      const healthCheckTimer = this.healthCheckTimers.get(instanceId);
      if (healthCheckTimer) {
        clearInterval(healthCheckTimer);
        this.healthCheckTimers.delete(instanceId);
      }
      
      // 从服务注册中心注销
      const metadata = status.metadata;
      this.serviceRegistry.unregisterInstance(
        metadata.serviceName,
        metadata.serverId,
        instanceId
      );
      
      // 发出停止事件
      await this.emitLifecycleEvent('stopping', {
        id: instanceId,
        serviceName: metadata.serviceName,
        serverId: metadata.serverId,
        instanceName: instanceId,
        host: metadata.host,
        port: metadata.port,
        healthy: false,
        weight: 1,
        connections: 0,
        responseTime: 0,
        lastHealthCheck: new Date(),
        registeredAt: new Date(),
        metadata,
      });
      
      // 执行实际的实例停止逻辑
      await this.performInstanceStop(instanceId, graceful);
      
      // 更新状态为已停止
      status.status = 'stopped';
      
      // 发出停止完成事件
      await this.emitLifecycleEvent('stopped', {
        id: instanceId,
        serviceName: metadata.serviceName,
        serverId: metadata.serverId,
        instanceName: instanceId,
        host: metadata.host,
        port: metadata.port,
        healthy: false,
        weight: 1,
        connections: 0,
        responseTime: 0,
        lastHealthCheck: new Date(),
        registeredAt: new Date(),
        metadata,
      });
      
      this.logger.log(`✅ 实例停止成功: ${instanceId}`);
      
    } catch (error) {
      this.logger.error(`❌ 实例停止失败: ${instanceId}`, error);
      status.status = 'failed';
      throw error;
    }
  }

  /**
   * 重启服务实例
   */
  async restartInstance(instanceId: string): Promise<void> {
    const status = this.instanceStatuses.get(instanceId);
    if (!status) {
      throw new Error(`实例不存在: ${instanceId}`);
    }
    
    this.logger.log(`🔄 重启实例: ${instanceId}`);
    
    // 检查重启次数限制
    if (status.restartCount >= this.maxRestartAttempts) {
      this.logger.error(`❌ 实例重启次数超限: ${instanceId}, 次数=${status.restartCount}`);
      throw new Error(`实例重启次数超限: ${instanceId}`);
    }
    
    try {
      // 停止实例
      await this.stopInstance(instanceId, true);
      
      // 等待重启延迟
      await this.sleep(this.restartDelay);
      
      // 重新构建配置
      const config: InstanceDeploymentConfig = {
        serviceName: status.metadata.serviceName,
        serverId: status.metadata.serverId,
        instanceIndex: status.metadata.instanceIndex,
        port: status.metadata.port,
        host: status.metadata.host,
      };
      
      // 启动实例
      await this.startInstance(config);
      
      // 增加重启计数
      status.restartCount++;
      
      this.logger.log(`✅ 实例重启成功: ${instanceId}, 重启次数=${status.restartCount}`);
      
    } catch (error) {
      this.logger.error(`❌ 实例重启失败: ${instanceId}`, error);
      status.restartCount++;
      throw error;
    }
  }

  /**
   * 获取实例状态
   */
  getInstanceStatus(instanceId: string): InstanceStatus | null {
    return this.instanceStatuses.get(instanceId) || null;
  }

  /**
   * 获取所有实例状态
   */
  getAllInstanceStatuses(): Map<string, InstanceStatus> {
    return new Map(this.instanceStatuses);
  }

  /**
   * 扩容服务实例
   */
  async scaleUp(serviceName: string, serverId: string, targetInstances: number): Promise<string[]> {
    this.logger.log(`📈 扩容服务: ${serviceName}@${serverId} -> ${targetInstances} 实例`);
    
    const currentInstances = this.serviceRegistry.getInstances(serviceName, serverId);
    const currentCount = currentInstances.length;
    
    if (targetInstances <= currentCount) {
      this.logger.warn(`⚠️ 目标实例数不大于当前实例数: ${targetInstances} <= ${currentCount}`);
      return [];
    }
    
    const newInstanceIds: string[] = [];
    const basePort = 3000 + this.getServicePortOffset(serviceName);
    
    for (let i = currentCount + 1; i <= targetInstances; i++) {
      const config: InstanceDeploymentConfig = {
        serviceName,
        serverId,
        instanceIndex: i,
        port: basePort + (i - 1) * 10,
      };
      
      try {
        const instanceId = await this.startInstance(config);
        newInstanceIds.push(instanceId);
      } catch (error) {
        this.logger.error(`❌ 扩容实例启动失败: ${serviceName}-${serverId}-${i}`, error);
      }
    }
    
    this.logger.log(`✅ 扩容完成: ${serviceName}@${serverId}, 新增 ${newInstanceIds.length} 个实例`);
    return newInstanceIds;
  }

  /**
   * 缩容服务实例
   */
  async scaleDown(serviceName: string, serverId: string, targetInstances: number): Promise<void> {
    this.logger.log(`📉 缩容服务: ${serviceName}@${serverId} -> ${targetInstances} 实例`);
    
    const currentInstances = this.serviceRegistry.getInstances(serviceName, serverId);
    const currentCount = currentInstances.length;
    
    if (targetInstances >= currentCount) {
      this.logger.warn(`⚠️ 目标实例数不小于当前实例数: ${targetInstances} >= ${currentCount}`);
      return;
    }
    
    // 选择要停止的实例（优先选择连接数少的）
    const instancesToStop = currentInstances
      .sort((a, b) => a.connections - b.connections)
      .slice(targetInstances);
    
    for (const instance of instancesToStop) {
      try {
        await this.stopInstance(instance.instanceName, true);
      } catch (error) {
        this.logger.error(`❌ 缩容实例停止失败: ${instance.instanceName}`, error);
      }
    }
    
    this.logger.log(`✅ 缩容完成: ${serviceName}@${serverId}, 停止 ${instancesToStop.length} 个实例`);
  }

  // ==================== 私有方法 ====================

  /**
   * 执行实际的实例启动逻辑
   */
  private async performInstanceStart(config: InstanceDeploymentConfig): Promise<void> {
    // 这里应该集成实际的容器编排系统（Docker、K8s等）
    // 目前使用模拟实现
    this.logger.debug(`🔧 执行实例启动: ${config.serviceName}-${config.serverId}-${config.instanceIndex}`);
    
    // 模拟启动延迟
    await this.sleep(2000);
    
    // 在实际实现中，这里会：
    // 1. 生成Docker Compose配置或K8s Deployment
    // 2. 启动容器或Pod
    // 3. 等待服务就绪
    // 4. 验证健康检查
  }

  /**
   * 执行实际的实例停止逻辑
   */
  private async performInstanceStop(instanceId: string, graceful: boolean): Promise<void> {
    this.logger.debug(`🔧 执行实例停止: ${instanceId}, 优雅=${graceful}`);
    
    // 模拟停止延迟
    await this.sleep(graceful ? 5000 : 1000);
    
    // 在实际实现中，这里会：
    // 1. 发送停止信号给容器或Pod
    // 2. 等待优雅关闭超时
    // 3. 强制终止（如果需要）
    // 4. 清理资源
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(instanceId: string, config: InstanceDeploymentConfig): void {
    const timer = setInterval(async () => {
      await this.performHealthCheck(instanceId, config);
    }, this.healthCheckInterval);
    
    this.healthCheckTimers.set(instanceId, timer);
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(instanceId: string, config: InstanceDeploymentConfig): Promise<void> {
    const status = this.instanceStatuses.get(instanceId);
    if (!status || status.status !== 'running') {
      return;
    }
    
    try {
      // 执行健康检查（HTTP请求或TCP连接测试）
      const isHealthy = await this.checkInstanceHealth(config);
      
      const wasHealthy = status.health === 'healthy';
      status.health = isHealthy ? 'healthy' : 'unhealthy';
      status.lastHealthCheck = new Date();
      
      // 更新服务注册中心的健康状态
      this.serviceRegistry.updateInstanceHealth(
        config.serviceName,
        config.serverId,
        instanceId,
        isHealthy
      );
      
      // 如果健康状态发生变化，考虑重启
      if (wasHealthy && !isHealthy) {
        this.logger.warn(`⚠️ 实例变为不健康: ${instanceId}`);
        
        // 添加到重启队列（避免重复重启）
        if (!this.restartQueue.has(instanceId)) {
          this.restartQueue.add(instanceId);
          
          // 延迟重启，给实例自我恢复的机会
          setTimeout(async () => {
            this.restartQueue.delete(instanceId);
            
            // 再次检查状态，如果仍然不健康则重启
            const currentStatus = this.instanceStatuses.get(instanceId);
            if (currentStatus && currentStatus.health === 'unhealthy') {
              try {
                await this.restartInstance(instanceId);
              } catch (error) {
                this.logger.error(`❌ 自动重启失败: ${instanceId}`, error);
              }
            }
          }, this.restartDelay);
        }
      }
      
    } catch (error) {
      this.logger.error(`❌ 健康检查异常: ${instanceId}`, error);
      status.health = 'unknown';
    }
  }

  /**
   * 检查实例健康状态
   */
  private async checkInstanceHealth(config: InstanceDeploymentConfig): Promise<boolean> {
    try {
      // 简单的TCP连接测试
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`http://${config.host || 'localhost'}:${config.port}/health/simple`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取服务端口偏移量
   */
  private getServicePortOffset(serviceName: string): number {
    const offsets: Record<string, number> = {
      auth: 0,
      character: 1,
      hero: 2,
      economy: 3,
      social: 4,
      activity: 5,
      match: 6,
    };
    
    return offsets[serviceName] || 10;
  }

  /**
   * 发出生命周期事件
   */
  private async emitLifecycleEvent(type: InstanceLifecycleEvent['type'], instance: ServerAwareServiceInstance): Promise<void> {
    const event: InstanceLifecycleEvent = {
      type,
      instance,
      timestamp: new Date(),
    };
    
    this.eventEmitter.emit('instance.lifecycle', event);
  }

  /**
   * 处理实例注册事件
   */
  private handleInstanceRegistered(event: any): void {
    this.logger.debug(`📝 实例注册事件: ${event.instance.instanceName}`);
  }

  /**
   * 处理实例注销事件
   */
  private handleInstanceUnregistered(event: any): void {
    this.logger.debug(`🗑️ 实例注销事件: ${event.instance.instanceName}`);
  }

  /**
   * 处理实例健康状态变化事件
   */
  private handleInstanceHealthChanged(event: any): void {
    this.logger.debug(`🏥 实例健康状态变化: ${event.instanceName} -> ${event.healthy ? '健康' : '不健康'}`);
  }

  /**
   * 优雅关闭所有实例
   */
  private async shutdownAllInstances(): Promise<void> {
    const instanceIds = Array.from(this.instanceStatuses.keys());
    
    if (instanceIds.length === 0) {
      return;
    }
    
    this.logger.log(`🛑 优雅关闭所有实例: ${instanceIds.length} 个`);
    
    const shutdownPromises = instanceIds.map(instanceId => 
      this.stopInstance(instanceId, true).catch(error => 
        this.logger.error(`❌ 关闭实例失败: ${instanceId}`, error)
      )
    );
    
    await Promise.allSettled(shutdownPromises);
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
