import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GlobalServiceRegistryService } from './global-service-registry.service';
import { ServerAwareLoadBalancerService } from './server-aware-load-balancer.service';

/**
 * 统一服务发现服务
 * 
 * 网关专用，自动识别服务类型并选择合适的发现方式：
 * - 全局服务：使用GlobalServiceRegistry
 * - 区服服务：使用ServerAwareRegistry
 * 
 * 核心特性：
 * - 智能服务类型识别
 * - 自动选择发现方式
 * - 统一的服务发现接口
 * - 支持服务发现缓存
 */
@Injectable()
export class UnifiedServiceDiscoveryService {
  private readonly logger = new Logger(UnifiedServiceDiscoveryService.name);
  
  // 全局服务列表（可通过环境变量配置）
  private readonly GLOBAL_SERVICES = new Set([
    'auth', 'payment', 'notification', 'analytics'
  ]);

  constructor(
    private readonly globalRegistry: GlobalServiceRegistryService,
    private readonly serverAwareLoadBalancer: ServerAwareLoadBalancerService,
    private readonly configService: ConfigService,
  ) {
    this.logger.log('🔍 统一服务发现服务启动');
  }

  /**
   * 统一服务发现入口
   * @param serviceName 服务名称
   * @param context 请求上下文（包含区服信息等）
   */
  async discoverService(
    serviceName: string, 
    context?: ServiceDiscoveryContext
  ): Promise<ServiceInstance> {
    
    if (this.isGlobalService(serviceName)) {
      return this.discoverGlobalService(serviceName);
    } else {
      return this.discoverServerService(serviceName, context?.serverId);
    }
  }

  /**
   * 发现全局服务
   */
  private async discoverGlobalService(serviceName: string): Promise<ServiceInstance> {
    this.logger.debug(`🌍 发现全局服务: ${serviceName}`);
    
    try {
      // 使用全局服务注册中心选择实例
      return await this.globalRegistry.selectInstance(serviceName);
    } catch (error) {
      this.logger.error(`❌ 全局服务发现失败: ${serviceName}`, error);
      throw new ServiceUnavailableException(`全局服务 ${serviceName} 不可用`);
    }
  }

  /**
   * 发现区服服务
   */
  private async discoverServerService(
    serviceName: string, 
    serverId?: string
  ): Promise<ServiceInstance> {
    const targetServerId = serverId || this.getDefaultServerId();
    this.logger.debug(`🏰 发现区服服务: ${serviceName}@${targetServerId}`);
    
    try {
      const instance = await this.serverAwareLoadBalancer.selectInstance(serviceName, targetServerId, 'round-robin');
      if (!instance) {
        throw new ServiceUnavailableException(`区服服务 ${serviceName}@${targetServerId} 不可用`);
      }

      // 转换为统一格式
      return {
        id: instance.id,
        serviceName: instance.serviceName,
        host: instance.host,
        port: instance.port,
        healthy: instance.healthy,
        weight: instance.weight,
        metadata: instance.metadata,
      };
    } catch (error) {
      this.logger.error(`❌ 区服服务发现失败: ${serviceName}@${targetServerId}`, error);
      throw new ServiceUnavailableException(`区服服务 ${serviceName}@${targetServerId} 不可用`);
    }
  }

  /**
   * 判断是否为全局服务
   */
  private isGlobalService(serviceName: string): boolean {
    return this.GLOBAL_SERVICES.has(serviceName);
  }

  /**
   * 获取默认区服ID
   */
  private getDefaultServerId(): string {
    return this.configService.get('DEFAULT_SERVER_ID', 'server_001');
  }

  /**
   * 获取服务发现统计信息
   */
  getDiscoveryStats(): ServiceDiscoveryStats {
    return {
      globalServices: Array.from(this.GLOBAL_SERVICES),
      defaultServerId: this.getDefaultServerId(),
      // 可以添加更多统计信息
    };
  }
}

/**
 * 服务发现上下文
 */
export interface ServiceDiscoveryContext {
  serverId?: string;
  userId?: string;
  requestId?: string;
}

/**
 * 服务实例接口
 */
export interface ServiceInstance {
  id: string;
  serviceName: string;
  host: string;
  port: number;
  healthy: boolean;
  weight: number;
  metadata?: Record<string, any>;
}

/**
 * 服务发现统计信息
 */
export interface ServiceDiscoveryStats {
  globalServices: string[];
  defaultServerId: string;
}

/**
 * 服务不可用异常
 */
export class ServiceUnavailableException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ServiceUnavailableException';
  }
}
