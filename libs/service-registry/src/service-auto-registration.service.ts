import { Injectable, Logger, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerAwareRegistryService } from './server-aware-registry.service';

/**
 * 服务自动注册服务
 * 
 * 借鉴 microservice-kit 的设计模式，提供零侵入性的服务注册功能。
 * 通过 NestJS 生命周期钩子自动处理服务注册和注销。
 * 
 * 核心特性：
 * - 零侵入性：业务代码无需修改
 * - 配置驱动：所有参数从环境变量获取
 * - 生命周期管理：自动注册和注销
 * - 错误容错：注册失败不影响服务启动
 */
@Injectable()
export class ServiceAutoRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceAutoRegistrationService.name);
  private instanceId: string | null = null;
  private serverId: string;
  private instanceName: string;

  constructor(
    @Inject('SERVICE_REGISTRATION_CONFIG') private readonly config: {
      serviceName: string;
      autoRegister: boolean;
      weight: number;
      metadata: Record<string, any>;
    },
    private readonly configService: ConfigService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
  ) {}

  async onModuleInit() {
    if (this.config.autoRegister) {
      await this.registerService();
    }
  }

  async onModuleDestroy() {
    if (this.instanceId) {
      await this.unregisterService();
    }
  }

  /**
   * 注册服务到区服感知服务注册中心
   */
  private async registerService(): Promise<void> {
    try {
      // 获取配置参数
      this.serverId = this.getServerId();
      this.instanceName = this.getInstanceName();
      const port = this.getServicePort();
      const environment = this.configService.get<string>('NODE_ENV', 'development');

      this.logger.log(`🏷️ 自动注册配置详情:`);
      this.logger.log(`   📋 服务名称: ${this.config.serviceName}`);
      this.logger.log(`   🏰 区服ID: ${this.serverId}`);
      this.logger.log(`   🏷️ 实例名称: ${this.instanceName}`);
      this.logger.log(`   🌐 监听地址: 127.0.0.1:${port}`);
      this.logger.log(`   ⚖️ 负载权重: ${this.config.weight}`);
      this.logger.log(`   🌍 运行环境: ${environment}`);
      this.logger.log(`   🔧 自动注册: ${this.config.autoRegister ? '启用' : '禁用'}`);

      // 执行注册
      this.instanceId = await this.serverAwareRegistry.registerInstance({
        serviceName: this.config.serviceName,
        serverId: this.serverId,
        instanceName: this.instanceName,
        host: '127.0.0.1',
        port: port,
        healthy: true,
        weight: this.config.weight,
        metadata: {
          version: '1.0.0',
          environment: environment,
          startTime: new Date().toISOString(),
          autoRegistered: true,
          registrationMode: 'ServiceRegistryModule.forServer',
          // 合并用户自定义元数据
          ...this.config.metadata,
        },
      });

      this.logger.log(`✅ 自动注册成功!`);
      this.logger.log(`   🆔 实例ID: ${this.instanceId}`);
      this.logger.log(`   📍 注册地址: service_registry:instances:${this.config.serviceName}:${this.serverId}`);
      this.logger.log(`   🔗 健康检查: http://127.0.0.1:${port}/health`);
      this.logger.log(`   📊 元数据: ${JSON.stringify(this.config.metadata, null, 2).replace(/\n/g, '\n      ')}`);
      this.logger.log(`🎉 ${this.config.serviceName} 服务已成功接入区服感知架构!`);
    } catch (error) {
      this.logger.error(`❌ 自动注册失败: ${error.message}`, error.stack);
      // 注册失败不应该阻止服务启动
    }
  }

  /**
   * 从服务注册中心注销服务
   */
  private async unregisterService(): Promise<void> {
    if (!this.instanceId || !this.serverId || !this.instanceName) {
      return;
    }

    try {
      const success = this.serverAwareRegistry.unregisterInstance(
        this.config.serviceName,
        this.serverId,
        this.instanceName
      );

      if (success) {
        this.logger.log(`✅ 自动注销成功: ${this.instanceName}`);
      } else {
        this.logger.warn(`⚠️ 自动注销失败: 实例不存在或已被移除`);
      }
    } catch (error) {
      this.logger.error(`❌ 自动注销失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取区服ID
   * 支持多种配置键名，优先级从高到低
   */
  private getServerId(): string {
    const serverId = this.configService.get('SERVER_ID') ||
                     this.configService.get('DEFAULT_SERVER_ID') ||
                     this.configService.get('CURRENT_SERVER_ID') ||
                     this.configService.get(`${this.config.serviceName.toUpperCase()}_SERVER_ID`) ||
                     'server_001';

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    return serverId;
  }

  /**
   * 获取实例名称
   */
  private getInstanceName(): string {
    const customInstanceId = this.configService.get('INSTANCE_ID') ||
                             this.configService.get(`${this.config.serviceName.toUpperCase()}_INSTANCE_ID`);
    
    if (customInstanceId) {
      return customInstanceId;
    }

    // 自动生成实例名称
    const instanceNumber = this.configService.get<number>('INSTANCE_NUMBER') ||
                           this.configService.get<number>(`${this.config.serviceName.toUpperCase()}_INSTANCE_NUMBER`) ||
                           1;
    
    return `${this.config.serviceName}-${this.serverId}-${instanceNumber}`;
  }

  /**
   * 获取服务端口
   * 支持服务特定端口配置
   */
  private getServicePort(): number {
    const serviceName = this.config.serviceName.toUpperCase();
    
    // 优先级：服务特定端口 > 通用端口 > 默认端口
    const port = this.configService.get<number>(`${serviceName}_PORT`) ||
                 this.configService.get<number>('PORT') ||
                 this.getDefaultPortByService(this.config.serviceName);

    return port;
  }

  /**
   * 根据服务名称获取默认端口
   */
  private getDefaultPortByService(serviceName: string): number {
    const defaultPorts: Record<string, number> = {
      'gateway': 3000,
      'auth': 3001,
      'character': 3002,
      'hero': 3003,
      'economy': 3004,
      'activity': 3005,
      'social': 3006,
      'match': 3007,
      'notification': 3008,
    };

    return defaultPorts[serviceName] || 3000;
  }

  /**
   * 获取当前服务注册状态
   */
  getRegistrationStatus(): {
    registered: boolean;
    serviceName: string;
    serverId: string | null;
    instanceName: string | null;
    instanceId: string | null;
    autoRegister: boolean;
  } {
    return {
      registered: !!this.instanceId,
      serviceName: this.config.serviceName,
      serverId: this.serverId || null,
      instanceName: this.instanceName || null,
      instanceId: this.instanceId || null,
      autoRegister: this.config.autoRegister,
    };
  }
}
