import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RedisModule } from '@common/redis';

// 核心服务
import { ServerAwareRegistryService } from './server-aware-registry.service';
import { ServerAwareLoadBalancerService } from './server-aware-load-balancer.service';

// 实例管理服务
import { InstanceLifecycleService } from './instance-lifecycle.service';
import { ServerConfigGeneratorService } from './server-config-generator.service';
import { ContainerOrchestrationService } from './container-orchestration.service';

/**
 * 区服感知服务注册中心模块
 * 
 * 提供分区分服架构下的服务注册与发现功能：
 * - 区服级别的服务实例注册
 * - 区服感知的服务发现
 * - 区服级别的负载均衡
 * - 健康检查和故障转移
 * 
 * 核心特性：
 * - 支持每个区服独立的服务实例集群
 * - 自动健康检查和超时实例清理
 * - 多种负载均衡策略
 * - 实时服务状态监控
 */
@Global()
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
    // 服务注册中心使用全局Redis配置，不使用服务特定的前缀
    RedisModule.forRootAsync({
      service: 'service-registry',
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
        // 使用空前缀，这样全局数据类型的键格式为：development:fm:global:xxx
        keyPrefix: '',
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    ServerAwareRegistryService,
    ServerAwareLoadBalancerService,
    InstanceLifecycleService,
    ServerConfigGeneratorService,
    ContainerOrchestrationService,
  ],
  exports: [
    ServerAwareRegistryService,
    ServerAwareLoadBalancerService,
    InstanceLifecycleService,
    ServerConfigGeneratorService,
    ContainerOrchestrationService,
  ],
})
export class ServiceRegistryModule {}
