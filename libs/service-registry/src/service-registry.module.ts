import { Module, Global, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RedisModule } from '@common/redis';

// 核心服务
import { ServerAwareRegistryService } from './server-aware-registry.service';
import { ServerAwareLoadBalancerService } from './server-aware-load-balancer.service';

// 实例管理服务
import { InstanceLifecycleService } from './instance-lifecycle.service';
import { ServerConfigGeneratorService } from './server-config-generator.service';
import { ContainerOrchestrationService } from './container-orchestration.service';

// 自动注册服务
import { ServiceAutoRegistrationService } from './service-auto-registration.service';

// 统一服务发现
import { UnifiedServiceDiscoveryService } from './unified-service-discovery.service';

// 全局服务注册
import { GlobalServiceRegistryService } from './global-service-registry.service';
import { GlobalServiceAutoRegistrationService } from './global-service-auto-registration.service';

/**
 * 统一服务注册中心模块
 *
 * 提供分层服务注册架构，支持全局服务和区服服务：
 * - 全局服务：跨区服共享，如Auth、Payment
 * - 区服服务：区服隔离，如Character、Hero
 * - 统一服务发现：智能识别服务类型
 *
 * 核心特性：
 * - 分层清晰：网关层、全局服务层、区服服务层
 * - 智能路由：自动选择合适的服务发现方式
 * - 负载均衡：全局和区服服务都支持负载均衡
 * - 零侵入性：业务代码最小化修改
 *
 * 使用模式（借鉴 microservice-kit 设计）：
 * - forRoot(): 网关模式，提供统一服务发现
 * - forGlobal(): 全局服务模式，如Auth
 * - forServer(): 区服服务模式，如Character
 * - forService(): 兼容模式，等同于forServer()
 */
@Module({})
export class ServiceRegistryModule {

  /**
   * 网关模式 - 提供统一服务发现能力（用于Gateway）
   * 自动识别全局服务和区服服务，智能路由
   */
  static forRoot(): DynamicModule {
    return {
      module: ServiceRegistryModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(),
        // 服务注册中心使用全局Redis配置
        RedisModule.forRootAsync({
          service: 'service-registry',
          useFactory: (configService: ConfigService) => ({
            host: configService.get('REDIS_HOST'),
            port: configService.get('REDIS_PORT'),
            password: configService.get('REDIS_PASSWORD'),
            keyPrefix: '',
          }),
          inject: [ConfigService],
        }),
      ],
      providers: [
        // 统一服务发现（网关专用）
        UnifiedServiceDiscoveryService,

        // 全局服务注册中心
        GlobalServiceRegistryService,

        // 区服感知服务注册中心
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        ServerConfigGeneratorService,
        ContainerOrchestrationService,
      ],
      exports: [
        UnifiedServiceDiscoveryService,  // 网关主要使用
        GlobalServiceRegistryService,
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        ServerConfigGeneratorService,
        ContainerOrchestrationService,
      ],
    };
  }

  /**
   * 全局服务模式 - 用于Auth等跨区服服务
   * 特点：数据全局共享，支持全局负载均衡
   */
  static forGlobal(
    serviceName: string,
    options?: {
      weight?: number;               // 负载均衡权重，默认1
      metadata?: Record<string, any>; // 额外元数据
      healthCheckPath?: string;      // 健康检查路径，默认/health
    }
  ): DynamicModule {
    return {
      module: ServiceRegistryModule,
      global: true,
      imports: [
        ConfigModule,
        // 🔧 修复：不创建新的Redis实例，复用现有的Redis服务
        // RedisModule已经在主应用中初始化，这里不需要重复创建
      ],
      providers: [
        GlobalServiceRegistryService,
        {
          provide: 'GLOBAL_SERVICE_CONFIG',
          useValue: {
            serviceName,
            weight: options?.weight ?? 1,
            metadata: options?.metadata ?? {},
            healthCheckPath: options?.healthCheckPath ?? '/health',
          },
        },
        GlobalServiceAutoRegistrationService,
      ],
      exports: [
        GlobalServiceRegistryService,
        GlobalServiceAutoRegistrationService,
      ],
    };
  }

  /**
   * 区服服务模式 - 用于Character等业务服务
   * 特点：数据区服隔离，支持区服内负载均衡
   */
  static forServer(
    serviceName: string,
    options?: {
      weight?: number;               // 负载均衡权重，默认1
      metadata?: Record<string, any>; // 额外元数据
      healthCheckPath?: string;      // 健康检查路径，默认/health
    }
  ): DynamicModule {
    return this.forService(serviceName, {
      autoRegister: true,
      weight: options?.weight,
      metadata: options?.metadata,
    });
  }


}
