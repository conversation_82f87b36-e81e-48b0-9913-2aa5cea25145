import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from '@economy/modules/health/health.module';
import { ShopModule } from '@economy/modules/shop/shop.module';
import { PaymentModule } from '@economy/modules/payment/payment.module';
import { CurrencyModule } from '@economy/modules/currency/currency.module';
import { TradeModule } from '@economy/modules/trade/trade.module';
import { ExchangeModule } from '@economy/modules/exchange/exchange.module';
import { RelayModule } from '@economy/modules/relay/relay.module';
import { LotteryModule } from '@economy/modules/lottery/lottery.module';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';
import { GameConfigModule } from '@app/game-config';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`, // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/economy/.env.redis',                          // 4. Redis业务配置
        'apps/economy/.env.security',                       // 5. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/economy/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/economy/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/economy/.env.local',                          // 6. 服务本地覆盖
      ],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'economy'),
        ...setupDatabaseEvents('economy'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'economy',
      serverId: 'server_001',
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 微服务公共库 - 混合模式（抽奖系统需要调用Character和Hero服务）
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.ECONOMY_SERVICE,  // 作为服务端的服务名
      {
        services: [                               // 作为客户端需要连接的服务
          MICROSERVICE_NAMES.CHARACTER_SERVICE,   // 需要调用角色服务（货币检查和扣除）
          MICROSERVICE_NAMES.HERO_SERVICE,        // 需要调用球员服务（创建球员）
        ],
      }
    ),

    // 功能模块
    HealthModule,
    ShopModule,
    PaymentModule,
    CurrencyModule,
    TradeModule,
    ExchangeModule,
    RelayModule,
    LotteryModule, // 传统抽奖系统
  ],
})
export class AppModule {}
