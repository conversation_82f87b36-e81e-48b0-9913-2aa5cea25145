#!/usr/bin/env node

/**
 * 角色编配测试脚本运行器
 * 
 * 使用方法：
 * 1. 确保所有服务已启动（Gateway、Auth、Character）
 * 2. 运行: node apps/gateway/scripts/run-character-test.js
 * 
 * 测试覆盖：
 * - 账号注册和登录
 * - 角色列表获取
 * - 角色创建（如果需要）
 * - 角色登录和Token生成
 * - WebSocket连接验证
 * - 微服务调用测试
 */

const CharacterProxyTester = require('./test-character-proxy');

/**
 * 检查服务健康状态
 */
async function checkServicesHealth() {
  const axios = require('axios');
  const services = [
    { name: 'Gateway', url: 'http://127.0.0.1:3000/health' },
    { name: 'Auth', url: 'http://127.0.0.1:3001/health' },
    { name: 'Character', url: 'http://127.0.0.1:3002/health' }
  ];

  console.log('🔍 检查服务健康状态...');
  
  for (const service of services) {
    try {
      const response = await axios.get(service.url, { timeout: 5000 });
      const status = response.data.status === 'ok' ? '✅' : '❌';
      console.log(`  ${status} ${service.name}: ${response.data.status || 'unknown'}`);
    } catch (error) {
      console.log(`  ❌ ${service.name}: 连接失败 (${error.message})`);
      return false;
    }
  }
  
  return true;
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 启动Gateway角色编配功能测试');
  console.log('=' .repeat(50));
  
  try {
    // 1. 检查服务健康状态
    const servicesHealthy = await checkServicesHealth();
    if (!servicesHealthy) {
      console.log('\n❌ 部分服务不可用，请检查服务状态后重试');
      process.exit(1);
    }
    
    console.log('\n✅ 所有服务健康检查通过，开始测试...\n');
    
    // 2. 运行角色编配测试
    const tester = new CharacterProxyTester();
    await tester.runAllTests();
    
    console.log('\n🎉 测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, checkServicesHealth };
