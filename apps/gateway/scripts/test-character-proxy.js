/**
 * Gateway角色管理代理功能测试脚本
 * 
 * 测试流程：
 * 1. 注册账号（复用HTTP代理代码）
 * 2. 登录获取账号级Token（复用HTTP代理代码）
 * 3. 获取指定区服的角色列表
 * 4. 如果有角色：生成角色级Token
 * 5. 如果没有角色：创建角色然后生成角色级Token
 * 6. 角色登录验证
 * 
 * 基于测试脚本开发指南的严格要求：
 * - 必须先分析真实业务代码
 * - 基于实际Controller、Service、DTO定义编写测试
 * - 使用正确的数据格式和错误处理
 */

const axios = require('axios');
const io = require('socket.io-client');

// 配置常量（基于真实环境配置）
const CONFIG = {
  GATEWAY_HTTP_URL: 'http://127.0.0.1:3000',
  GATEWAY_WS_URL: 'ws://127.0.0.1:3000',
  AUTH_SERVICE_URL: 'http://127.0.0.1:3001',
  // 基于实际业务需求的测试数据
  TEST_USER: {
    username: `test_char_user_${Date.now()}`,
    email: `test_char_${Date.now()}@example.com`,
    password: 'SecureP@ssw0rd!', // 符合密码策略
    firstName: 'Character',
    lastName: 'Tester'
  },
  // 基于gateway配置的区服信息
  TEST_SERVER: {
    serverId: 'server_001', // 基于gateway.config.ts中的默认区服
    serverName: '新手村'
  },
  // 基于CreateCharacterRequestDto的角色创建数据
  TEST_CHARACTER: {
    name: `测试角色${Date.now().toString().slice(-6)}`, // 限制在12个字符以内
    faceIcon: 15, // 1-100范围内
    qualified: 4  // 1-5范围内
  }
};

/**
 * 角色编配测试类
 * 
 * 基于真实业务逻辑的完整测试流程
 */
class CharacterProxyTester {
  constructor() {
    this.ws = null;
    this.testResults = [];
    this.accountToken = null;      // 账号级Token
    this.characterToken = null;    // 角色级Token
    this.userId = null;
    this.characterId = null;
    this.characterInfo = null;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始Gateway角色编配功能测试');
    console.log('=' .repeat(60));
    console.log(`👤 测试用户: ${CONFIG.TEST_USER.username}`);
    console.log(`🏰 测试区服: ${CONFIG.TEST_SERVER.serverId} (${CONFIG.TEST_SERVER.serverName})`);
    console.log(`🎭 测试角色: ${CONFIG.TEST_CHARACTER.name}`);

    try {
      // 阶段1：账号注册和登录（获取账号级Token）
      await this.testAccountAuthentication();

      // 阶段2：角色编配流程
      await this.testCharacterManagement();

      // 阶段3：角色登录验证
      await this.testCharacterLogin();

      // 阶段4：WebSocket连接测试（使用角色Token）
      await this.testWebSocketWithCharacterToken();

      // 输出测试结果
      this.printTestResults();

    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  /**
   * 阶段1：账号认证测试（复用HTTP代理代码）
   */
  async testAccountAuthentication() {
    console.log('\n🔐 阶段1：账号认证测试');

    // 1.1 注册账号（直接调用Auth服务，避免Gateway代理问题）
    try {
      console.log('🔍 注册请求数据:', {
        username: CONFIG.TEST_USER.username,
        email: CONFIG.TEST_USER.email,
        password: '[REDACTED]'
      });

      const registerResponse = await this.makeRequest('POST', `${CONFIG.AUTH_SERVICE_URL}/auth/register`, {
        username: CONFIG.TEST_USER.username,
        email: CONFIG.TEST_USER.email,
        password: CONFIG.TEST_USER.password,
        confirmPassword: CONFIG.TEST_USER.password,
        acceptTerms: true,
        profile: {
          firstName: CONFIG.TEST_USER.firstName,
          lastName: CONFIG.TEST_USER.lastName,
          language: 'zh-CN'
        }
      });

      this.recordTest('账号注册', registerResponse.success,
        `注册结果: ${registerResponse.message || '注册成功'}`);
    } catch (error) {
      this.recordTest('账号注册', false,
        `注册失败: ${error.response?.data?.message || error.message}`);
    }

    // 1.2 账号登录（通过Gateway代理）
    try {
      const loginResponse = await this.makeRequest('POST', `${CONFIG.GATEWAY_HTTP_URL}/user/login`, {
        username: CONFIG.TEST_USER.username,
        password: CONFIG.TEST_USER.password
      });

      console.log('✅ 登录响应:', JSON.stringify(loginResponse, null, 2));

      if (loginResponse.success) {
        // 根据实际返回的数据结构获取token（参考成功脚本的解析方式）
        const outerData = loginResponse.data || loginResponse;
        const innerData = outerData.data || outerData;

        // 从tokens对象中获取token
        if (innerData.tokens) {
          this.accountToken = innerData.tokens.accessToken;
        } else if (innerData.data && innerData.data.tokens) {
          this.accountToken = innerData.data.tokens.accessToken;
        }

        // 从JWT token中获取用户ID
        if (this.accountToken) {
          try {
            const tokenPayload = JSON.parse(atob(this.accountToken.split('.')[1]));
            this.userId = tokenPayload.sub || tokenPayload.userId;
          } catch (e) {
            console.warn('无法解析JWT token，尝试从用户对象获取ID');
          }
        }

        // 如果从token获取失败，尝试从用户对象获取
        if (!this.userId) {
          if (innerData.user && innerData.user.id) {
            this.userId = innerData.user.id;
          } else if (innerData.data && innerData.data.user && innerData.data.user.id) {
            this.userId = innerData.data.user.id;
          }
        }

        if (this.accountToken) {
          this.recordTest('账号登录', true,
            `Token获取成功，用户ID: ${this.userId || 'N/A'}`);
        } else {
          this.recordTest('账号登录', false,
            `登录成功但未获得Token，数据结构: ${JSON.stringify(innerData)}`);
        }
      } else {
        this.recordTest('账号登录', false,
          `登录失败: ${loginResponse.message || '未知错误'}`);
      }
    } catch (error) {
      this.recordTest('账号登录', false,
        `登录异常: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 阶段2：角色管理测试
   */
  async testCharacterManagement() {
    console.log('\n🎭 阶段2：角色管理测试');

    if (!this.accountToken) {
      this.recordTest('角色管理测试', false, '缺少账号Token');
      return;
    }

    // 2.1 获取角色列表
    try {
      const listResponse = await this.makeRequest('GET', 
        `${CONFIG.GATEWAY_HTTP_URL}/character/list?serverId=${CONFIG.TEST_SERVER.serverId}`, 
        null, 
        { 'Authorization': `Bearer ${this.accountToken}` }
      );

      console.log('✅ 角色列表响应:', JSON.stringify(listResponse, null, 2));

      if (listResponse.success) {
        const characters = listResponse.characters || [];
        this.recordTest('获取角色列表', true,
          `角色数量: ${characters.length}`);

        // 2.2 根据角色列表决定后续流程
        if (characters.length > 0) {
          // 有角色：使用现有角色
          this.characterId = characters[0].characterId;
          this.characterInfo = characters[0];
          console.log(`📋 使用现有角色: ${this.characterInfo.name} (${this.characterId})`);
        } else {
          // 没有角色：创建新角色
          await this.createCharacter();
        }
      } else {
        this.recordTest('获取角色列表', false,
          `获取失败: ${listResponse.message || '未知错误'}`);
      }
    } catch (error) {
      this.recordTest('获取角色列表', false,
        `获取异常: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 创建角色（当角色列表为空时调用）
   */
  async createCharacter() {
    console.log('\n🎨 创建新角色');

    try {
      const createResponse = await this.makeRequest('POST', 
        `${CONFIG.GATEWAY_HTTP_URL}/character/create`, 
        {
          serverId: CONFIG.TEST_SERVER.serverId,
          name: CONFIG.TEST_CHARACTER.name,
          faceIcon: CONFIG.TEST_CHARACTER.faceIcon,
          qualified: CONFIG.TEST_CHARACTER.qualified
        },
        { 'Authorization': `Bearer ${this.accountToken}` }
      );

      console.log('✅ 创建角色响应:', JSON.stringify(createResponse, null, 2));

      if (createResponse.success) {
        // 解析Gateway的嵌套响应结构
        const outerData = createResponse.data || createResponse;
        const character = outerData.character || outerData.data;

        if (character && character.characterId) {
          this.characterId = character.characterId;
          this.characterInfo = character;
          this.recordTest('创建角色', true,
            `角色创建成功: ${this.characterInfo.name} (${this.characterId})`);
        } else {
          this.recordTest('创建角色', false,
            `创建成功但未获得角色信息，数据结构: ${JSON.stringify(outerData)}`);
        }
      } else {
        this.recordTest('创建角色', false,
          `创建失败: ${createResponse.message || '未知错误'}`);
      }
    } catch (error) {
      this.recordTest('创建角色', false,
        `创建异常: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 阶段3：角色登录测试
   */
  async testCharacterLogin() {
    console.log('\n🚪 阶段3：角色登录测试');

    if (!this.accountToken || !this.characterId) {
      this.recordTest('角色登录测试', false, '缺少必要的认证信息或角色ID');
      return;
    }

    try {
      const loginResponse = await this.makeRequest('POST', 
        `${CONFIG.GATEWAY_HTTP_URL}/character/login`, 
        {
          serverId: CONFIG.TEST_SERVER.serverId,
          characterId: this.characterId
        },
        { 'Authorization': `Bearer ${this.accountToken}` }
      );

      console.log('✅ 角色登录响应:', JSON.stringify(loginResponse, null, 2));

      if (loginResponse.success) {
        // 解析Gateway的嵌套响应结构
        const outerData = loginResponse.data || loginResponse;
        const innerData = outerData.data || outerData;

        if (innerData.characterToken) {
          this.characterToken = innerData.characterToken;
          this.characterInfo = innerData.character;
          this.sessionInfo = innerData.session;
          this.recordTest('角色登录', true,
            `角色Token获取成功，过期时间: ${innerData.expiresAt}`);

          // 验证角色Token
          await this.validateCharacterToken();
        } else {
          this.recordTest('角色登录', false,
            `登录成功但未获得Token，数据结构: ${JSON.stringify(innerData)}`);
        }
      } else {
        this.recordTest('角色登录', false,
          `登录失败: ${loginResponse.message || '未知错误'}`);
      }
    } catch (error) {
      this.recordTest('角色登录', false,
        `登录异常: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 验证角色Token
   */
  async validateCharacterToken() {
    if (!this.characterToken) {
      this.recordTest('角色Token验证', false, '没有角色Token可供验证');
      return;
    }

    try {
      const validateResponse = await this.makeRequest('POST', 
        `${CONFIG.GATEWAY_HTTP_URL}/character/validate-token`, 
        { characterToken: this.characterToken },
        { 'Authorization': `Bearer ${this.accountToken}` }
      );

      console.log('✅ Token验证响应:', JSON.stringify(validateResponse, null, 2));

      if (validateResponse.success) {
        // 解析Gateway的嵌套响应结构
        const outerData = validateResponse.data || validateResponse;
        const isValid = outerData.valid === true;
        const message = outerData.message || '验证完成';

        this.recordTest('角色Token验证', isValid,
          `验证结果: ${isValid ? '✅ ' + message : '❌ ' + message}`);
      } else {
        this.recordTest('角色Token验证', false,
          `验证失败: ${validateResponse.message || '未知错误'}`);
      }
    } catch (error) {
      this.recordTest('角色Token验证', false,
        `验证异常: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 阶段4：WebSocket连接测试（使用角色Token）
   */
  async testWebSocketWithCharacterToken() {
    console.log('\n🔌 阶段4：WebSocket连接测试（角色Token）');

    if (!this.characterToken) {
      this.recordTest('WebSocket角色连接', false, '缺少角色Token');
      return;
    }

    try {
      await this.connectWebSocketWithCharacterAuth();
      this.recordTest('WebSocket角色连接', true, '角色Token认证连接成功');

      // 测试微服务调用（使用角色Token）
      await this.testMicroserviceCallsWithCharacterToken();
    } catch (error) {
      this.recordTest('WebSocket角色连接', false,
        `连接失败: ${error.message}`);
    }
  }

  /**
   * 测试微服务调用（使用角色Token）
   */
  async testMicroserviceCallsWithCharacterToken() {
    if (!this.ws || !this.ws.connected) {
      this.recordTest('角色微服务调用', false, 'WebSocket未连接');
      return;
    }

    console.log('\n📡 测试角色微服务调用');

    // 测试获取角色详细信息
    try {
      const characterInfoResponse = await this.callMicroservice('character.getInfo', {
        characterId: this.characterId,
        serverId: CONFIG.TEST_SERVER.serverId
      });

      const isSuccess = characterInfoResponse.payload && !characterInfoResponse.payload.error;
      this.recordTest('获取角色详细信息', isSuccess,
        `微服务调用: ${isSuccess ? '成功' : (characterInfoResponse.payload?.error || '未知错误')}`);
    } catch (error) {
      this.recordTest('获取角色详细信息', false, `调用异常: ${error.message}`);
    }
  }

  /**
   * 建立WebSocket连接（使用角色Token认证）
   */
  async connectWebSocketWithCharacterAuth() {
    return new Promise((resolve, reject) => {
      console.log('🔌 建立WebSocket连接（角色Token认证）...');

      this.ws = io(CONFIG.GATEWAY_WS_URL, {
        auth: { token: this.characterToken },  // 使用角色Token进行认证
        transports: ['websocket', 'polling'],
        timeout: 30000,
        forceNew: true
      });

      this.ws.on('connect', () => {
        console.log('✅ Socket.IO角色连接和认证成功');
        resolve();
      });

      this.ws.on('connect_error', (error) => {
        console.error('❌ Socket.IO角色连接失败:', error.message);
        reject(error);
      });

      this.ws.on('disconnect', (reason) => {
        console.log('🔌 Socket.IO角色连接断开:', reason);
      });

      this.ws.on('error', (error) => {
        console.error('❌ Socket.IO角色错误:', error);
      });

      // 监听消息
      this.ws.on('message', (data) => {
        console.log('📨 收到Socket.IO消息:', data);
      });

      // 设置连接超时
      setTimeout(() => {
        if (!this.ws.connected) {
          reject(new Error('WebSocket角色连接超时'));
        }
      }, 30000);
    });
  }

  /**
   * 调用微服务（通过Socket.IO - 复用WebSocket代理代码）
   */
  async callMicroservice(command, payload = {}) {
    return new Promise((resolve, reject) => {
      if (!this.ws || !this.ws.connected) {
        reject(new Error('Socket.IO未连接'));
        return;
      }

      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 构建正确的消息格式
      const message = {
        id: messageId,
        command: command,
        payload: payload
      };

      console.log(`📤 发送微服务调用: ${command}`);
      console.log(JSON.stringify(message, null, 2));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(`📨 收到微服务响应: ${command}`);
          console.log(JSON.stringify(response, null, 2));
          this.ws.off('message', responseHandler);
          resolve(response);
        }
      };

      this.ws.on('message', responseHandler);

      // 发送消息
      this.ws.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.ws.off('message', responseHandler);
        reject(new Error('微服务调用超时'));
      }, 30000);
    });
  }

  /**
   * 发送HTTP请求的通用方法（复用HTTP代理代码）
   */
  async makeRequest(method, url, data = null, headers = {}) {
    try {
      console.log(`\n🔄 ${method.toUpperCase()} ${url}`);
      if (data) {
        console.log(`📤 请求数据:`, JSON.stringify(data, null, 2));
      }

      const config = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        timeout: 30000
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);

      console.log(`✅ 响应状态: ${response.status}`);
      console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));

      return response.data;
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
      if (error.response) {
        console.log(`📥 错误响应 (${error.response.status}):`, JSON.stringify(error.response.data, null, 2));
        return error.response.data;
      }
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, success, details = '') {
    const result = {
      name: testName,
      success,
      details,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);

    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${testName}: ${details}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 Gateway角色编配功能测试结果汇总');
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`\n📈 总体统计:`);
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过数: ${passedTests} ✅`);
    console.log(`  失败数: ${failedTests} ❌`);
    console.log(`  通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log(`\n❌ 失败的测试:`);
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.details}`);
        });
    }

    console.log(`\n🎯 角色编配功能验证结果:`);
    if (passedTests >= totalTests * 0.8) {
      console.log('✅ Gateway角色编配功能基本正常');
      console.log(`🎭 测试角色信息: ${this.characterInfo?.name || 'N/A'} (${this.characterId || 'N/A'})`);
      console.log(`🔑 账号Token: ${this.accountToken ? this.accountToken.substring(0, 20) + '...' : 'N/A'}`);
      console.log(`🎫 角色Token: ${this.characterToken ? this.characterToken.substring(0, 20) + '...' : 'N/A'}`);
    } else {
      console.log('❌ Gateway角色编配功能存在问题，需要修复');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new CharacterProxyTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CharacterProxyTester;
