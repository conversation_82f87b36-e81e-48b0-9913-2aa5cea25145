import { Injectable, Logger, BadRequestException, NotFoundException, ConflictException } from '@nestjs/common';
import { MicroserviceClientService } from '@common/microservice-kit';
import { 
  CharacterInfoDto, 
  CreateCharacterRequestDto, 
  CharacterLoginRequestDto,
  GetCharacterListRequestDto,
  CharacterLoginResponseDto,
  CharacterListResponseDto,
  CreateCharacterResponseDto 
} from '../dto/character.dto';

/**
 * 网关层角色服务
 * 
 * 职责：
 * 1. 编排角色相关的微服务调用
 * 2. 处理角色创建、登录的业务流程
 * 3. 验证角色归属和权限
 * 4. 统一错误处理和响应格式
 */
@Injectable()
export class CharacterService {
  private readonly logger = new Logger(CharacterService.name);

  constructor(
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 获取用户在指定区服的角色列表
   */
  async getCharacterList(
    userId: string, 
    request: GetCharacterListRequestDto
  ): Promise<CharacterListResponseDto> {
    this.logger.log(`📋 获取角色列表: userId=${userId}, serverId=${request.serverId}`);

    try {
      // 1. 验证区服状态
      const server = await this.validateServerStatus(request.serverId);

      // 2. 调用Character微服务获取角色列表
      const response = await this.microserviceClient.call('character', 'character.getList', {
        userId,
        serverId: request.serverId,
      });

      // 3. 解析Character服务返回的数据格式
      let characters = [];
      if (response && response.data && response.data.list) {
        characters = response.data.list;
      } else if (Array.isArray(response)) {
        // 兼容直接返回数组的情况
        characters = response;
      } else if (response && Array.isArray(response.data)) {
        // 兼容data字段直接是数组的情况
        characters = response.data;
      }

      this.logger.log(`✅ 获取角色列表成功: userId=${userId}, 角色数量=${characters.length}`);

      return {
        success: true,
        message: '获取角色列表成功',
        characters: characters.map(this.mapToCharacterInfoDto),
        totalCharacters: characters.length,
        server: {
          id: server.id,
          name: server.name,
          status: server.status,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 获取角色列表失败: userId=${userId}, serverId=${request.serverId}`, error);
      throw error;
    }
  }

  /**
   * 创建新角色
   */
  async createCharacter(
    userId: string, 
    request: CreateCharacterRequestDto
  ): Promise<CreateCharacterResponseDto> {
    this.logger.log(`🎭 创建角色: userId=${userId}, serverId=${request.serverId}, name=${request.name}`);

    try {
      // 1. 验证区服状态
      const server = await this.validateServerStatus(request.serverId);

      // 2. 检查角色名称是否已存在
      await this.validateCharacterName(request.serverId, request.name);

      // 3. 检查用户在该区服的角色数量限制
      await this.validateCharacterLimit(userId, request.serverId);

      // 4. 调用Character微服务创建角色
      const response = await this.microserviceClient.call('character', 'character.create', {
        userId,
        serverId: request.serverId,
        openId: `test_openid_${userId}_${Date.now()}`, // 为测试环境生成openId
        name: request.name,
        faceIcon: request.faceIcon,
        qualified: request.qualified || 3, // 默认资质为3
      });

      // 解析Character服务的响应格式 { code, message, data }
      if (response.code !== 0) {
        throw new BadRequestException(response.message || '创建角色失败');
      }

      const character = response.data;
      this.logger.log(`✅ 创建角色成功: characterId=${character.characterId}, name=${character.name}`);

      return {
        success: true,
        message: '角色创建成功',
        character: this.mapToCharacterInfoDto(character),
        server: {
          id: server.id,
          name: server.name,
          status: server.status,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 创建角色失败: userId=${userId}, serverId=${request.serverId}`, error);
      throw error;
    }
  }

  /**
   * 角色登录
   */
  async characterLogin(
    userId: string,
    request: CharacterLoginRequestDto
  ): Promise<CharacterLoginResponseDto> {
    this.logger.log(`🚪 角色登录: userId=${userId}, serverId=${request.serverId}, characterId=${request.characterId}`);

    try {
      // 1. 验证区服状态
      const server = await this.validateServerStatus(request.serverId);

      // 2. 验证角色存在且属于当前用户
      const character = await this.validateCharacterOwnership(
        userId,
        request.serverId,
        request.characterId
      );

      // 3. 调用Auth微服务生成角色Token
      const tokenResponse = await this.microserviceClient.call('auth', 'character-auth.generateCharacterToken', {
        userId,
        characterId: character.characterId,
        serverId: request.serverId,
        characterName: character.name,
      });

      // 解析Auth服务的响应格式 { success, message, data }
      if (!tokenResponse.success) {
        throw new BadRequestException(tokenResponse.message || '生成角色Token失败');
      }

      const tokenResult = tokenResponse.data;

      // 4. 更新角色最后活跃时间
      await this.updateCharacterLastActive(character.characterId);

      this.logger.log(`✅ 角色登录成功: characterId=${character.characterId}, name=${character.name}`);

      return {
        success: true,
        message: '角色登录成功',
        characterToken: tokenResult.characterToken,
        expiresIn: tokenResult.expiresIn,
        expiresAt: tokenResult.expiresAt,
        character: this.mapToCharacterInfoDto(character),
        server: {
          id: server.id,
          name: server.name,
          status: server.status,
        },
        session: {
          id: tokenResult.session.id,
          expiresAt: tokenResult.session.expiresAt,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 角色登录失败: userId=${userId}, characterId=${request.characterId}`, error);
      throw error;
    }
  }

  /**
   * 验证角色Token
   * 调用Auth微服务验证角色Token的有效性
   */
  async validateCharacterToken(characterToken: string): Promise<{
    valid: boolean;
    payload?: any;
    message: string;
    character?: CharacterInfoDto;
    server?: { id: string; name: string; status: string };
  }> {
    this.logger.log(`🔍 验证角色Token`);

    try {
      // 调用Auth微服务验证角色Token
      const verificationResult = await this.microserviceClient.call('auth', 'character-auth.verifyCharacterToken', {
        characterToken,
      });

      if (!verificationResult.valid) {
        this.logger.warn(`❌ 角色Token验证失败: ${verificationResult.error}`);
        return {
          valid: false,
          message: verificationResult.error || 'Token无效或已过期',
        };
      }

      const payload = verificationResult.data; // Auth服务返回的是data字段，不是payload字段
      this.logger.log(`✅ 角色Token验证成功: characterId=${payload.characterId}, serverId=${payload.serverId}`);

      // 可选：获取角色详细信息
      let character: CharacterInfoDto | undefined;
      let server: { id: string; name: string; status: string } | undefined;

      try {
        // 获取角色信息
        const characterResponse = await this.microserviceClient.call('character', 'character.getInfo', {
          characterId: payload.characterId,
          serverId: payload.serverId,
        });

        // 解析Character服务的响应格式 { code, message, data }
        const characterData = characterResponse.code === 0 ? characterResponse.data : null;

        if (characterData) {
          character = this.mapToCharacterInfoDto(characterData);
        }

        // 获取区服信息
        server = await this.validateServerStatus(payload.serverId);
      } catch (error) {
        // 获取角色或区服信息失败不影响Token验证结果
        this.logger.warn(`⚠️ 获取角色或区服信息失败: characterId=${payload.characterId}`, error);
      }

      return {
        valid: true,
        message: 'Token验证成功',
        payload: {
          userId: payload.userId,
          characterId: payload.characterId,
          serverId: payload.serverId,
          characterName: payload.characterName,
          scope: payload.scope,
          type: payload.type,
          sessionId: payload.sessionId,
          deviceId: payload.deviceId,
          iat: payload.iat,
          exp: payload.exp,
          jti: payload.jti,
        },
        character,
        server,
      };
    } catch (error) {
      this.logger.error(`❌ 验证角色Token时发生错误:`, error);
      return {
        valid: false,
        message: 'Token验证失败，请稍后重试',
      };
    }
  }

  /**
   * 私有方法：验证区服状态
   */
  private async validateServerStatus(serverId: string): Promise<any> {
    // 这里可以调用服务发现或配置服务获取区服状态
    // 暂时返回模拟数据
    return {
      id: serverId,
      name: `区服${serverId}`,
      status: 'active',
    };
  }

  /**
   * 私有方法：验证角色名称是否已存在
   */
  private async validateCharacterName(serverId: string, name: string): Promise<void> {
    const searchResult = await this.microserviceClient.call('character', 'character.searchByName', {
      serverId,
      name,
    });

    // 检查搜索结果，如果找到角色说明名称已存在
    if (searchResult && searchResult.data) {
      throw new ConflictException('角色名称已存在，请选择其他名称');
    }
  }

  /**
   * 私有方法：验证用户角色数量限制
   */
  private async validateCharacterLimit(userId: string, serverId: string): Promise<void> {
    const characters = await this.microserviceClient.call('character', 'character.getList', {
      userId,
      serverId,
    });

    const maxCharacters = 1; // 每个区服最多1个角色
    if (characters.length >= maxCharacters) {
      throw new BadRequestException(`每个区服最多只能创建${maxCharacters}个角色`);
    }
  }

  /**
   * 私有方法：验证角色归属
   */
  private async validateCharacterOwnership(
    userId: string,
    serverId: string,
    characterId: string
  ): Promise<any> {
    const response = await this.microserviceClient.call('character', 'character.getInfo', {
      characterId,
      serverId,
    });

    // 解析Character服务的响应格式 { code, message, data }
    if (response.code !== 0) {
      throw new NotFoundException(response.message || '角色不存在');
    }

    const character = response.data;

    if (!character) {
      throw new NotFoundException('角色不存在');
    }

    if (character.userId !== userId) {
      throw new BadRequestException('角色不属于当前用户');
    }

    if (character.status === 'banned') {
      throw new BadRequestException('角色已被封禁，无法登录');
    }

    return character;
  }

  /**
   * 私有方法：更新角色最后活跃时间
   */
  private async updateCharacterLastActive(characterId: string): Promise<void> {
    try {
      await this.microserviceClient.call('character', 'character.updateLastActive', {
        characterId,
        lastActiveAt: new Date(),
      });
    } catch (error) {
      // 更新活跃时间失败不影响登录流程
      this.logger.warn(`⚠️ 更新角色活跃时间失败: characterId=${characterId}`, error);
    }
  }

  /**
   * 私有方法：映射到DTO
   */
  private mapToCharacterInfoDto(character: any): CharacterInfoDto {
    return {
      characterId: character.characterId,
      userId: character.userId,
      serverId: character.serverId,
      name: character.name,
      level: character.level || 1,
      faceIcon: character.faceIcon || 1,
      createdAt: character.createdAt,
      lastActiveAt: character.lastActiveAt,
      status: character.status || 'active',
      isNewbie: character.isNewbie || false,
    };
  }
}
