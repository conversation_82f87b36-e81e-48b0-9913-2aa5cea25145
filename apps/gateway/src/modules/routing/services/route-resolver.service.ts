import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ROUTE_CONSTANTS } from '../../../common/constants';
import { MICROSERVICE_NAMES } from '@shared/constants';
// 🔧 优化：使用统一服务发现
import { UnifiedServiceDiscoveryService } from '@libs/service-registry';

/**
 * 智能路由解析器
 * 
 * 实现基于前缀的智能路由匹配，支持回退机制：
 * 1. 匹配到前缀时，提取服务名
 * 2. 如果服务名存在于微服务映射中，代理到对应微服务
 * 3. 如果服务名不存在，回退到网关系统路由
 */
export interface RouteResult {
  type: 'proxy' | 'internal' | 'not_found' | 'service_unavailable';
  targetService?: string;
  originalPath: string;
  targetPath: string;
  fallback?: boolean;
  error?: string;
  statusCode?: number;
}

@Injectable()
export class RouteResolverService {
  private readonly logger = new Logger(RouteResolverService.name);
  private readonly PROXY_PREFIX = ROUTE_CONSTANTS.PROXY_PREFIX;

  // 支持的微服务集合（直接使用常量）
  private readonly supportedMicroservices = new Set<string>(Object.values(MICROSERVICE_NAMES));

  constructor(
    // 🔧 优化：使用统一服务发现
    private readonly serviceDiscovery: UnifiedServiceDiscoveryService,
    private readonly configService: ConfigService,
  ) {}
  
  /**
   * 解析路由（异步版本，支持区服感知服务发现）
   * @param path 请求路径
   * @returns 路由解析结果
   */
  async resolveRoute(path: string): Promise<RouteResult> {
    if (path.startsWith(this.PROXY_PREFIX)) {
      // 尝试微服务代理
      const serviceName = this.extractServiceName(path);

      if (!this.supportedMicroservices.has(serviceName)) {
        // 未知服务
        this.logger.debug(`Unknown service: ${serviceName} for path ${path}`);
        return {
          type: 'not_found',
          originalPath: path,
          targetPath: path,
          error: `Unknown service: ${serviceName}`,
          statusCode: 404
        };
      }

      // 🔧 优化：检查服务是否可用（使用统一服务发现）
      const serverId = this.configService.get('SERVER_ID', 'server_001');

      try {
        const instance = await this.serviceDiscovery.discoverService(serviceName, { serverId });
        if (!instance) {
          // 服务不可用
          this.logger.debug(`Service unavailable: ${serviceName} for path ${path}`);
          return {
            type: 'service_unavailable',
            targetService: serviceName,
            originalPath: path,
            targetPath: path,
            error: `Service ${serviceName} is not available`,
            statusCode: 503
          };
        }
      } catch (error) {
        this.logger.error(`Error checking service availability: ${serviceName}`, error);
        return {
          type: 'service_unavailable',
          targetService: serviceName,
          originalPath: path,
          targetPath: path,
          error: `Service ${serviceName} is not available`,
          statusCode: 503
        };
      }

      // 找到对应微服务且服务可用，进行代理
      this.logger.debug(`Route to microservice: ${path} -> ${serviceName}`);
      return {
        type: 'proxy',
        targetService: serviceName,
        originalPath: path,
        targetPath: path,
        fallback: false
      };
    } else {
      // 直接的网关内部路由
      this.logger.debug(`Internal route: ${path}`);
      return {
        type: 'internal',
        originalPath: path,
        targetPath: path,
        fallback: false
      };
    }
  }
  
  /**
   * 从路径中提取服务名
   * @param path 请求路径
   * @returns 服务名
   */
  private extractServiceName(path: string): string {
    // /api/auth/login -> auth
    // /api/docs -> docs (不在微服务映射中，会回退)
    const segments = path.split('/');
    return segments[2] || '';
  }
  
  /**
   * 检查是否为微服务路由
   * @param path 请求路径
   * @returns 是否为微服务路由
   */
  isMicroserviceRoute(path: string): boolean {
    if (!path.startsWith(this.PROXY_PREFIX)) {
      return false;
    }

    const serviceName = this.extractServiceName(path);
    return this.supportedMicroservices.has(serviceName);
  }
  
  /**
   * 获取支持的微服务列表
   * @returns 微服务名称数组
   */
  getSupportedMicroservices(): string[] {
    return Array.from(this.supportedMicroservices);
  }

  /**
   * 检查服务是否被支持
   * @param serviceName 服务名称
   * @returns 是否支持该服务
   */
  isServiceSupported(serviceName: string): boolean {
    return this.supportedMicroservices.has(serviceName);
  }
}
