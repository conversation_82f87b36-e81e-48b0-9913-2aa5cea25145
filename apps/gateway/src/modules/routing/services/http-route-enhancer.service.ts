import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

// 服务
import { HttpContextExtractorService, HttpRequestContext } from './http-context-extractor.service';
import { LoadBalancingService } from '../../load-balancing/services/load-balancing.service';

// 区服感知服务注册与发现
import { ServerAwareRegistryService, ServerAwareLoadBalancerService } from '@libs/service-registry';

// 类型定义
export interface EnhancedRouteResult {
  targetUrl: string;
  serviceName: string;
  originalServiceName: string;
  routingStrategy: 'normal' | 'server_specific' | 'cross_server' | 'global';
  context: HttpRequestContext;
  headers: Record<string, string>;
  requiresAuth: boolean;
  serverRouting: boolean;
  selectedInstance?: {
    instanceName: string;
    host: string;
    port: number;
    serverId: string;
  };
}

/**
 * HTTP路由增强器
 * 为HTTP代理请求添加区服路由支持
 * 
 * 核心功能：
 * 1. 路由策略分析：分析请求的路由需求
 * 2. 服务名增强：为需要区服路由的服务添加区服标识
 * 3. 头部注入：注入区服和用户上下文头部
 * 4. 负载均衡：选择合适的服务实例
 */
@Injectable()
export class HttpRouteEnhancerService {
  private readonly logger = new Logger(HttpRouteEnhancerService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpContextExtractor: HttpContextExtractorService,
    private readonly loadBalancerService: LoadBalancingService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
    private readonly serverAwareLoadBalancer: ServerAwareLoadBalancerService,
  ) {}

  /**
   * 增强HTTP路由
   */
  async enhanceRoute(
    req: Request,
    originalServiceName: string,
    originalTargetUrl: string
  ): Promise<EnhancedRouteResult> {
    this.logger.debug(`🔧 Enhancing route for ${originalServiceName}: ${req.method} ${req.path}`);

    try {
      // 1. 提取请求上下文
      const context = this.httpContextExtractor.extractContext(req);
      const contextSummary = this.httpContextExtractor.getContextSummary(context);
      
      this.logger.debug(`📍 Request context: ${contextSummary}`);

      // 2. 分析路由策略
      const routingStrategy = this.analyzeRoutingStrategy(req, context, originalServiceName);
      
      this.logger.debug(`📋 Routing strategy: ${routingStrategy}`);

      // 3. 确定目标服务名（保持原服务名，不再修改）
      const serviceName = originalServiceName;

      // 4. 选择服务实例（区服感知）
      const selectedInstance = await this.selectServiceInstance(serviceName, context, routingStrategy);

      // 5. 获取目标URL（基于选择的实例）
      const targetUrl = selectedInstance
        ? `http://${selectedInstance.host}:${selectedInstance.port}`
        : await this.getTargetUrl(serviceName, originalTargetUrl);

      // 6. 准备增强头部
      const headers = this.prepareEnhancedHeaders(req, context, routingStrategy);

      // 7. 检查是否需要认证
      const requiresAuth = this.requiresAuthentication(req, originalServiceName);

      // 8. 检查是否需要区服路由
      const serverRouting = this.httpContextExtractor.requiresServerRouting(req, context);

      const result: EnhancedRouteResult = {
        targetUrl,
        serviceName,
        originalServiceName,
        routingStrategy,
        context,
        headers,
        requiresAuth,
        serverRouting,
        selectedInstance,
      };

      this.logger.debug(`✅ Route enhanced: ${originalServiceName} -> ${serviceName} (${routingStrategy})`);
      return result;

    } catch (error) {
      this.logger.error(`❌ Failed to enhance route for ${originalServiceName}:`, error);
      
      // 返回基础路由结果
      return {
        targetUrl: originalTargetUrl,
        serviceName: originalServiceName,
        originalServiceName,
        routingStrategy: 'normal',
        context: {},
        headers: {},
        requiresAuth: false,
        serverRouting: false,
      };
    }
  }

  /**
   * 分析路由策略
   */
  private analyzeRoutingStrategy(
    req: Request,
    context: HttpRequestContext,
    serviceName: string
  ): 'normal' | 'server_specific' | 'cross_server' | 'global' {
    // 检查是否为跨服请求
    if (this.httpContextExtractor.isCrossServerRequest(req)) {
      return 'cross_server';
    }

    // 检查是否为全服请求
    if (this.httpContextExtractor.isGlobalRequest(req)) {
      return 'global';
    }

    // 检查是否需要区服特定路由
    if (this.httpContextExtractor.supportsServerRouting(serviceName) && context.serverId) {
      return 'server_specific';
    }

    // 默认为普通路由
    return 'normal';
  }

  /**
   * 确定目标服务名
   */
  private determineServiceName(
    originalServiceName: string,
    context: HttpRequestContext,
    routingStrategy: string
  ): string {
    switch (routingStrategy) {
      case 'server_specific':
        if (context.serverId) {
          return this.httpContextExtractor.getServerSpecificServiceName(originalServiceName, context.serverId);
        }
        break;
      
      case 'cross_server':
        // 跨服请求使用特殊的跨服服务名
        return `${originalServiceName}-cross`;
      
      case 'global':
        // 全服请求使用全服服务名
        return `${originalServiceName}-global`;
    }

    return originalServiceName;
  }

  /**
   * 获取目标URL
   */
  private async getTargetUrl(serviceName: string, originalTargetUrl: string): Promise<string> {
    try {
      // 使用负载均衡器获取服务实例
      const serviceInstances = this.loadBalancerService.getHealthyInstances(serviceName);
      const serviceInstance = serviceInstances.length > 0 ? serviceInstances[0] : null;
      
      if (serviceInstance) {
        // 使用服务实例的URL，替换原始URL的主机部分
        const originalUrl = new URL(originalTargetUrl);
        const instanceUrl = new URL(serviceInstance.url);
        return `${instanceUrl.protocol}//${instanceUrl.host}${originalUrl.pathname}${originalUrl.search}`;
      }

      // 如果没有找到服务实例，返回原始URL
      this.logger.warn(`⚠️ No healthy instance found for service: ${serviceName}, using original URL`);
      return originalTargetUrl;

    } catch (error) {
      this.logger.error(`❌ Failed to get target URL for service: ${serviceName}`, error);
      return originalTargetUrl;
    }
  }

  /**
   * 准备增强头部
   */
  private prepareEnhancedHeaders(
    req: Request,
    context: HttpRequestContext,
    routingStrategy: string
  ): Record<string, string> {
    const headers: Record<string, string> = {};

    // 添加基础上下文头部
    if (context.userId) {
      headers['X-User-Id'] = context.userId;
    }

    if (context.tokenScope) {
      headers['X-Token-Scope'] = context.tokenScope;
    }

    if (context.sessionId) {
      headers['X-Session-Id'] = context.sessionId;
    }

    // 添加区服相关头部
    if (context.serverId) {
      headers['X-Server-Id'] = context.serverId;
    }

    if (context.characterId) {
      headers['X-Character-Id'] = context.characterId;
    }

    // 添加路由策略头部
    headers['X-Routing-Strategy'] = routingStrategy;
    headers['X-Request-Id'] = this.generateRequestId();
    headers['X-Gateway-Timestamp'] = new Date().toISOString();

    // 根据路由策略添加特殊头部
    switch (routingStrategy) {
      case 'cross_server':
        headers['X-Cross-Server'] = 'true';
        if (context.serverId) {
          headers['X-Source-Server'] = context.serverId;
        }
        break;

      case 'global':
        headers['X-Global-Request'] = 'true';
        headers['X-All-Servers'] = 'true';
        break;

      case 'server_specific':
        headers['X-Server-Specific'] = 'true';
        break;
    }

    // 保留原始请求的重要头部
    const preserveHeaders = [
      'content-type',
      'accept',
      'user-agent',
      'accept-language',
      'accept-encoding',
    ];

    preserveHeaders.forEach(headerName => {
      const value = req.headers[headerName];
      if (value) {
        headers[headerName] = Array.isArray(value) ? value[0] : value;
      }
    });

    return headers;
  }

  /**
   * 检查是否需要认证
   */
  private requiresAuthentication(req: Request, serviceName: string): boolean {
    // 公开端点不需要认证
    const publicPaths = [
      '/health',
      '/metrics',
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
    ];

    const path = req.path.toLowerCase();
    if (publicPaths.some(publicPath => path.startsWith(publicPath))) {
      return false;
    }

    // 大部分服务都需要认证
    const authRequiredServices = [
      'character',
      'hero',
      'match',
      'guild',
      'economy',
      'social',
      'activity',
    ];

    return authRequiredServices.includes(serviceName);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证增强结果
   */
  validateEnhancedResult(result: EnhancedRouteResult): boolean {
    // 检查必需字段
    if (!result.targetUrl || !result.serviceName) {
      return false;
    }

    // 如果需要认证，检查是否有用户上下文
    if (result.requiresAuth && !result.context.userId) {
      return false;
    }

    // 如果需要区服路由，检查是否有区服上下文
    if (result.serverRouting && result.routingStrategy === 'server_specific' && !result.context.serverId) {
      return false;
    }

    return true;
  }

  /**
   * 获取路由摘要（用于日志）
   */
  getRouteSummary(result: EnhancedRouteResult): string {
    const parts = [
      `service: ${result.originalServiceName} -> ${result.serviceName}`,
      `strategy: ${result.routingStrategy}`,
    ];

    if (result.context.serverId) {
      parts.push(`server: ${result.context.serverId}`);
    }

    if (result.context.characterId) {
      parts.push(`character: ${result.context.characterId}`);
    }

    return `[${parts.join(', ')}]`;
  }

  /**
   * 选择服务实例（区服感知）
   */
  private async selectServiceInstance(
    serviceName: string,
    context: HttpRequestContext,
    routingStrategy: string
  ): Promise<{ instanceName: string; host: string; port: number; serverId: string } | null> {
    try {
      // Auth服务不分区服，使用传统负载均衡
      if (serviceName === 'auth') {
        this.logger.debug(`🔐 Auth服务使用传统负载均衡`);
        return null; // 使用原有的负载均衡机制
      }

      // 提取区服ID
      const serverId = this.extractServerIdFromContext(context);
      if (!serverId) {
        this.logger.warn(`⚠️ 无法确定区服ID，使用默认路由: ${serviceName}`);
        return null;
      }

      // 使用区服感知负载均衡器选择实例
      const instance = await this.serverAwareLoadBalancer.selectInstance(
        serviceName,
        serverId,
        'round-robin',
        {
          counterKey: `http-${serviceName}-${serverId}`,
          userId: context.userId,
        }
      );

      if (!instance) {
        this.logger.error(`❌ 没有可用的健康实例: ${serviceName}@${serverId}`);
        return null;
      }

      this.logger.debug(`🎯 选择HTTP实例: ${instance.instanceName} (${serviceName}@${serverId})`);

      return {
        instanceName: instance.instanceName,
        host: instance.host,
        port: instance.port,
        serverId: instance.serverId,
      };

    } catch (error) {
      this.logger.error(`❌ 选择服务实例失败: ${serviceName}`, error);
      return null;
    }
  }

  /**
   * 从上下文中提取区服ID
   */
  private extractServerIdFromContext(context: HttpRequestContext): string | null {
    // 1. 从上下文中直接获取
    if (context.serverId) {
      return context.serverId;
    }

    // 2. 从JWT Token中获取（如果是角色Token）
    if (context.tokenScope === 'character' && context.serverId) {
      return context.serverId;
    }

    // 4. 默认区服（开发环境）
    const defaultServerId = this.configService.get<string>('gateway.defaultServerId');
    if (defaultServerId) {
      this.logger.debug(`使用默认区服ID: ${defaultServerId}`);
      return defaultServerId;
    }

    return null;
  }
}
