import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 微服务通信
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES, MicroserviceName } from '@shared/constants';

// 上下文服务
import { ServerContextService } from '../context/server-context.service';
import { PayloadEnhancerService } from '../context/payload-enhancer.service';

// 服务注册与发现
import { ServerAwareRegistryService, ServerAwareLoadBalancerService } from '@libs/service-registry';

// 类型定义
export type RoutingStrategy = 'normal' | 'cross_server' | 'global' | 'system';

export interface RouteResult {
  success: boolean;
  data?: any;
  error?: string;
  strategy: RoutingStrategy;
  executionTime: number;
}

/**
 * 消息路由服务
 * 负责WebSocket消息的智能路由和分发
 * 
 * 核心功能：
 * 1. 路由策略分析：根据service和action决定路由类型
 * 2. 消息分发：将消息路由到正确的微服务
 * 3. 上下文注入：自动注入区服和用户上下文
 * 4. 跨服处理：处理跨服消息的特殊路由
 * 5. 全服处理：处理全服消息的广播路由
 */
@Injectable()
export class MessageRouterService {
  private readonly logger = new Logger(MessageRouterService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly microserviceClient: MicroserviceClientService,
    private readonly serverContextService: ServerContextService,
    private readonly payloadEnhancerService: PayloadEnhancerService,
    private readonly serviceRegistry: ServerAwareRegistryService,
    private readonly loadBalancer: ServerAwareLoadBalancerService,
  ) {}

  /**
   * 路由消息到适当的微服务
   * 主要入口方法
   */
  async routeMessage(
    service: string,
    action: string,
    payload: any,
    userId: string,
    clientContext?: any
  ): Promise<RouteResult> {
    const startTime = Date.now();
    this.logger.debug(`🔍 Routing message: ${service}.${action} for user ${userId}`);

    try {
      // 1. 分析路由策略
      const strategy = this.analyzeRoutingStrategy(service, action);
      this.logger.debug(`📋 Routing strategy: ${strategy}`);

      // 2. 增强payload（注入上下文）
      const enhancedPayload = await this.payloadEnhancerService.enhancePayload(
        payload,
        userId,
        clientContext,
        strategy
      );

      // 3. 执行路由策略
      const result = await this.executeRoutingStrategy(
        service,
        action,
        enhancedPayload,
        strategy
      );

      const executionTime = Date.now() - startTime;
      this.logger.debug(`✅ Message routed successfully in ${executionTime}ms`);

      return {
        success: true,
        data: result,
        strategy,
        executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(`❌ Message routing failed: ${error.message}`, error);

      return {
        success: false,
        error: error.message,
        strategy: 'normal',
        executionTime,
      };
    }
  }

  /**
   * 分析路由策略
   * 根据service和action决定路由类型
   */
  analyzeRoutingStrategy(service: string, action: string): RoutingStrategy {
    // 跨服消息（cross.前缀）
    if (service.startsWith('cross.') || action.startsWith('cross.')) {
      return 'cross_server';
    }
    
    // 全服消息（global.前缀）
    if (service.startsWith('global.') || action.startsWith('global.')) {
      return 'global';
    }
    
    // 系统消息（system.前缀）
    if (service.startsWith('system.') || action.startsWith('system.')) {
      return 'system';
    }
    
    // 普通区服消息（默认）
    return 'normal';
  }

  /**
   * 执行路由策略
   */
  private async executeRoutingStrategy(
    service: string,
    action: string,
    payload: any,
    strategy: RoutingStrategy
  ): Promise<any> {
    switch (strategy) {
      case 'cross_server':
        return await this.handleCrossServerRoute(service, action, payload);
      case 'global':
        return await this.handleGlobalRoute(service, action, payload);
      case 'system':
        return await this.handleSystemRoute(service, action, payload);
      default:
        return await this.handleNormalRoute(service, action, payload);
    }
  }

  /**
   * 处理普通区服路由（重构为区服感知）
   */
  private async handleNormalRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`🎯 Handling server-aware route: ${service}.${action}`);

    // 验证服务名称
    if (!this.isValidServiceName(service)) {
      throw new Error(`Invalid service name: ${service}`);
    }

    // 提取区服ID
    const serverId = this.extractServerId(payload);
    if (!serverId) {
      throw new Error(`无法确定区服ID，请确保用户已选择角色或提供区服上下文`);
    }

    // 选择服务实例
    const serviceInstance = await this.loadBalancer.selectInstance(service, serverId, 'round-robin', {
      counterKey: `${service}-${serverId}`,
      userId: payload.userId,
      requestId: payload.requestId,
    });

    if (!serviceInstance) {
      throw new Error(`没有可用的健康实例: ${service}@${serverId}`);
    }

    this.logger.debug(`🎯 选择服务实例: ${serviceInstance.instanceName} (${service}@${serverId})`);

    try {
      // 调用具体的服务实例
      const result = await this.microserviceClient.callInstance(
        serviceInstance,
        action,
        payload
      );

      this.logger.debug(`✅ Server-aware route completed successfully`);
      return result;
    } catch (error) {
      // 标记实例为不健康（如果是连接错误）
      if (this.isConnectionError(error)) {
        this.serviceRegistry.updateInstanceHealth(service, serverId, serviceInstance.instanceName, false);
        this.logger.warn(`❌ 标记实例为不健康: ${serviceInstance.instanceName} - ${error.message}`);
      }

      throw error;
    } finally {
      // 释放实例连接
      this.loadBalancer.releaseInstance(service, serverId, serviceInstance.instanceName);
    }
  }

  /**
   * 处理跨服路由
   */
  private async handleCrossServerRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`🌐 Handling cross-server route: ${service}.${action}`);
    
    // 移除cross.前缀
    const actualService = service.replace(/^cross\./, '');
    const actualAction = action.replace(/^cross\./, '');
    
    // 验证服务名称
    if (!this.isValidServiceName(actualService)) {
      throw new Error(`Invalid service name: ${actualService}`);
    }

    // 添加跨服标记
    const crossServerPayload = {
      ...payload,
      _crossServer: true,
      _sourceServer: payload.serverContext?.serverId,
      _messageType: 'cross_server',
    };

    // 调用跨服处理服务
    const result = await this.microserviceClient.call(
      actualService as MicroserviceName,
      actualAction,
      crossServerPayload
    );

    this.logger.debug(`✅ Cross-server route completed successfully`);
    return result;
  }

  /**
   * 处理全服路由
   */
  private async handleGlobalRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`🌍 Handling global route: ${service}.${action}`);
    
    // 移除global.前缀
    const actualService = service.replace(/^global\./, '');
    const actualAction = action.replace(/^global\./, '');
    
    // 验证服务名称
    if (!this.isValidServiceName(actualService)) {
      throw new Error(`Invalid service name: ${actualService}`);
    }

    // 添加全服标记
    const globalPayload = {
      ...payload,
      _global: true,
      _allServers: true,
      _messageType: 'global',
    };

    // 调用全服处理服务
    const result = await this.microserviceClient.call(
      actualService as MicroserviceName,
      actualAction,
      globalPayload
    );

    this.logger.debug(`✅ Global route completed successfully`);
    return result;
  }

  /**
   * 处理系统路由
   */
  private async handleSystemRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`⚙️ Handling system route: ${service}.${action}`);
    
    // 移除system.前缀
    const actualService = service.replace(/^system\./, '');
    const actualAction = action.replace(/^system\./, '');
    
    // 验证服务名称
    if (!this.isValidServiceName(actualService)) {
      throw new Error(`Invalid service name: ${actualService}`);
    }

    // 系统消息不需要区服上下文
    const systemPayload = {
      ...payload,
      _system: true,
      _messageType: 'system',
    };

    // 调用系统处理服务
    const result = await this.microserviceClient.call(
      actualService as MicroserviceName,
      actualAction,
      systemPayload
    );

    this.logger.debug(`✅ System route completed successfully`);
    return result;
  }

  /**
   * 验证服务名称是否有效
   */
  private isValidServiceName(service: string): boolean {
    const validServices = Object.values(MICROSERVICE_NAMES);
    return validServices.includes(service as MicroserviceName);
  }

  /**
   * 从payload中提取区服ID
   */
  private extractServerId(payload: any): string | null {
    // 1. 从serverContext中获取
    if (payload.serverContext?.serverId) {
      return payload.serverContext.serverId;
    }

    // 2. 从用户上下文中获取（如果用户已选择角色）
    if (payload.userId) {
      const userContext = this.serverContextService.getUserContext(payload.userId);
      if (userContext?.serverId) {
        return userContext.serverId;
      }
    }

    // 3. 从JWT Token中获取（如果是角色Token）
    if (payload.tokenPayload?.scope === 'character' && payload.tokenPayload?.serverId) {
      return payload.tokenPayload.serverId;
    }

    // 4. 默认区服（开发环境）
    const defaultServerId = this.configService.get<string>('gateway.defaultServerId');
    if (defaultServerId) {
      this.logger.debug(`使用默认区服ID: ${defaultServerId}`);
      return defaultServerId;
    }

    return null;
  }

  /**
   * 判断是否为连接错误
   */
  private isConnectionError(error: any): boolean {
    const connectionErrorCodes = [
      'ECONNREFUSED',
      'ECONNRESET',
      'ETIMEDOUT',
      'ENOTFOUND',
      'EHOSTUNREACH',
    ];

    return connectionErrorCodes.some(code =>
      error.code === code ||
      error.message?.includes(code) ||
      error.message?.includes('connect') ||
      error.message?.includes('timeout')
    );
  }

  /**
   * 获取路由统计信息
   */
  async getRoutingStats(): Promise<any> {
    // 这里可以实现路由统计功能
    // 例如各种路由策略的使用频率、成功率、平均响应时间等
    return {
      totalRoutes: 0,
      normalRoutes: 0,
      crossServerRoutes: 0,
      globalRoutes: 0,
      systemRoutes: 0,
      avgResponseTime: 0,
      successRate: 0,
    };
  }
}
