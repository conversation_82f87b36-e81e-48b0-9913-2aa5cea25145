import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerAwareRegistryService } from '@libs/service-registry';

/**
 * 服务注册管理服务
 * 负责Character服务在区服感知服务注册中心的注册和注销
 */
@Injectable()
export class ServiceRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceRegistrationService.name);
  private instanceId: string | null = null;
  private serverId: string;
  private instanceName: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly serverAwareRegistry: ServerAwareRegistryService,
  ) {}

  async onModuleInit() {
    await this.registerService();
  }

  async onModuleDestroy() {
    await this.unregisterService();
  }

  /**
   * 注册服务到区服感知服务注册中心
   */
  private async registerService(): Promise<void> {
    try {
      // 获取区服配置
      this.serverId = this.getServerId();
      this.instanceName = this.getInstanceName();
      
      const port = this.configService.get<number>('CHARACTER_PORT', 3002);
      const environment = this.configService.get<string>('NODE_ENV', 'development');
      
      this.logger.log(`🏷️ 区服配置: ${this.serverId} (${this.instanceName})`);

      // 注册服务实例
      this.instanceId = await this.serverAwareRegistry.registerInstance({
        serviceName: 'character',
        serverId: this.serverId,
        instanceName: this.instanceName,
        host: '127.0.0.1',
        port: port,
        healthy: true,
        weight: this.configService.get<number>('INSTANCE_WEIGHT', 1),
        metadata: {
          version: '1.0.0',
          environment: environment,
          startTime: new Date().toISOString(),
          maxConnections: this.configService.get<number>('INSTANCE_MAX_CONNECTIONS', 1000),
          serverName: this.configService.get<string>('SERVER_NAME', '未命名区服'),
          serverRegion: this.configService.get<string>('SERVER_REGION', 'unknown'),
        },
      });

      this.logger.log(`✅ 已注册到区服感知服务注册中心: ${this.instanceName} (ID: ${this.instanceId})`);
    } catch (error) {
      this.logger.error(`❌ 区服感知服务注册失败: ${error.message}`, error.stack);
      // 注册失败不应该阻止服务启动，但需要记录错误
    }
  }

  /**
   * 从服务注册中心注销服务
   */
  private async unregisterService(): Promise<void> {
    if (!this.instanceId || !this.serverId || !this.instanceName) {
      return;
    }

    try {
      const success = this.serverAwareRegistry.unregisterInstance(
        'character',
        this.serverId,
        this.instanceName
      );

      if (success) {
        this.logger.log(`✅ 已从区服感知服务注册中心注销: ${this.instanceName}`);
      } else {
        this.logger.warn(`⚠️ 服务注销失败: 实例不存在或已被移除`);
      }
    } catch (error) {
      this.logger.error(`❌ 服务注销失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取区服ID
   */
  private getServerId(): string {
    const serverId = this.configService.get('SERVER_ID') || 
                     this.configService.get('DEFAULT_SERVER_ID') || 
                     this.configService.get('CURRENT_SERVER_ID') || 
                     this.configService.get('CHARACTER_SERVER_ID') || 
                     'server_001';

    // 验证区服ID格式
    if (!/^server_\d{3}$/.test(serverId)) {
      this.logger.warn(`⚠️ 区服ID格式不标准: ${serverId}，建议使用 server_xxx 格式`);
    }

    return serverId;
  }

  /**
   * 获取实例名称
   */
  private getInstanceName(): string {
    const customInstanceId = this.configService.get('INSTANCE_ID');
    if (customInstanceId) {
      return customInstanceId;
    }

    // 自动生成实例名称
    const serverId = this.getServerId();
    const instanceNumber = this.configService.get<number>('INSTANCE_NUMBER', 1);
    return `character-${serverId}-${instanceNumber}`;
  }

  /**
   * 获取当前服务注册状态
   */
  getRegistrationStatus(): {
    registered: boolean;
    serverId: string | null;
    instanceName: string | null;
    instanceId: string | null;
  } {
    return {
      registered: !!this.instanceId,
      serverId: this.serverId || null,
      instanceName: this.instanceName || null,
      instanceId: this.instanceId || null,
    };
  }
}
